import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Pattern, Phase, Stage } from '../types'

export const usePatternStore = defineStore('pattern', () => {
  // 状态
  const patternList = ref<Pattern[]>([])
  const phaseList = ref<Phase[]>([])
  const isStageMode = ref(false)
  
  // 计算属性
  const ringCount = computed(() => {
    const rings = phaseList.value.map(phase => phase.ring)
    return Math.max(...rings, 0)
  })
  
  // 方法
  const addPattern = () => {
    const newId = getNextId()
    const newPattern: Pattern = {
      id: newId,
      desc: `Pattern ${newId}`,
      offset: 0,
      cycle: 0,
      stagesList: [],
      rings: Array(ringCount.value).fill([])
    }
    patternList.value.push(newPattern)
    sortPatterns()
  }
  
  const deletePattern = (id: number) => {
    const index = patternList.value.findIndex(p => p.id === id)
    if (index > -1) {
      patternList.value.splice(index, 1)
    }
  }
  
  const clonePattern = (id: number) => {
    const pattern = patternList.value.find(p => p.id === id)
    if (pattern) {
      const newId = getNextId()
      const clonedPattern = {
        ...JSON.parse(JSON.stringify(pattern)),
        id: newId,
        desc: `${pattern.desc} (Copy)`
      }
      patternList.value.push(clonedPattern)
      sortPatterns()
    }
  }
  
  const addStage = (patternId: number) => {
    const pattern = patternList.value.find(p => p.id === patternId)
    if (pattern && pattern.stagesList.length < 16) {
      const newStage: Stage = {
        key: pattern.stagesList.length,
        stageNo: pattern.stagesList.length + 1,
        green: 25,
        yellow: 3,
        red: 2,
        min: 15,
        max: 60,
        phases: [],
        stageSplit: 30
      }
      pattern.stagesList.push(newStage)
      updatePatternCycle(patternId)
    }
  }
  
  const updateStage = (patternId: number, stageIndex: number, stage: Stage) => {
    const pattern = patternList.value.find(p => p.id === patternId)
    if (pattern && pattern.stagesList[stageIndex]) {
      pattern.stagesList[stageIndex] = { ...stage }
      updatePatternCycle(patternId)
    }
  }
  
  const setStageMode = (mode: boolean) => {
    isStageMode.value = mode
  }
  
  const initializeData = () => {
    // 初始化数据逻辑
    loadPhaseList()
    loadPatternList()
  }
  
  // 辅助方法
  const getNextId = (): number => {
    const existingIds = patternList.value.map(p => p.id)
    for (let i = 1; i <= 108; i++) {
      if (!existingIds.includes(i)) {
        return i
      }
    }
    return 1
  }
  
  const sortPatterns = () => {
    patternList.value.sort((a, b) => a.id - b.id)
  }
  
  const updatePatternCycle = (patternId: number) => {
    const pattern = patternList.value.find(p => p.id === patternId)
    if (pattern) {
      if (isStageMode.value) {
        pattern.cycle = pattern.stagesList.reduce((sum, stage) => 
          sum + stage.green + stage.yellow + stage.red, 0)
      }
    }
  }
  
  const loadPhaseList = () => {
    // 从 API 或 store 加载相位列表
  }
  
  const loadPatternList = () => {
    // 从 API 或 store 加载方案列表
  }
  
  return {
    // 状态
    patternList,
    phaseList,
    isStageMode,
    
    // 计算属性
    ringCount,
    
    // 方法
    addPattern,
    deletePattern,
    clonePattern,
    addStage,
    updateStage,
    setStageMode,
    initializeData
  }
})