<template>
  <div class="terminal-lock-container" v-show="isConfigShow">
    <div class="terminal-header">
      <div class="title">
        <el-tooltip v-if="curDevice && curDevice.name && curDevice.name.length > 24" placement="bottom-start" effect="dark">
          <div slot="content">{{curDevice.name}}</div>
          <span class="name">{{curDevice.name.substr(0, 20) + '...'}}</span>
        </el-tooltip>
        <span v-else>{{curDevice ? curDevice.name : ''}}</span>
        <span class="platform">{{curDevice ? curDevice.platform : ''}}</span>
      </div>
      <div :class="isShowVideo ? 'switch':'switch ext'" v-if="isConfigShow && zoomBiz === 'play'">
        <el-button :class="getExecClass()" type="text" @click="onSwitchIntersectionBtnClick(preDevice)" v-if="getIntersectionName(preDevice)">上游路口：{{ getIntersectionName(preDevice) }}</el-button>
        <el-button :class="'btn-next ' + getExecClass()" type="text" @click="onSwitchIntersectionBtnClick(nextDevice)" v-if="getIntersectionName(nextDevice)">下游路口：{{ getIntersectionName(nextDevice)}}</el-button>
        <el-button :class="getExecClass()" type="text" @click="onaExecBtnClick(1)">执行</el-button>
        <el-button :class="getExecClass()" type="text" @click="onaExecBtnClick(0)">恢复</el-button>
        <el-button :class="getBizClass('terminal')"  icon="iconfont icon-tongdao"  type="text" @click="onSwitchBizClick('terminal')">灯态</el-button>
        <el-button
          @click="onModifyVideosClick"
          icon="el-icon-edit"
          type="text"
          :class="getBizClass('terminal')"
          v-show="isShowVideo"
          >修改路口视频</el-button>
        <!-- <el-button v-show="isShowVideo" :class="getBizClass('video')" icon="iconfont icon-bofang" type="text" @click="onSwitchBizClick('video')">{{$t('openatc.faultrecord.video')}}</el-button> -->
      </div>
      <div class="close" @click="onLeftCloseClick"><i class="el-icon-close"></i></div>
    </div>
    <div class="content">
      <div v-if="isConfigShow && zoomBiz === 'play'" class="videos">
        <div class="top-left"><!-- 北 3-->
          <Flv v-if="isReload && videos3 && videos3.length > 0" :curDevVideos="videos3" :autoPlay="autoPlay" Height="auto"></Flv>
          <!-- <button class="plus-button">+</button> -->
          <el-button
            v-else
            style="z-index: 9999999;"
            class="add-btn"
            type="primary"
            icon="el-icon-plus"
            @click="onAddClick(3)"></el-button>
        </div>
        <div class="top-right"><!-- 东 0-->
          <Flv v-if="isReload && videos0 && videos0.length > 0" :curDevVideos="videos0" :autoPlay="autoPlay" Height="auto"></Flv>
          <el-button
            v-else
            style="z-index: 9999999;"
            class="add-btn"
            type="primary"
            icon="el-icon-plus"
            @click="onAddClick(0)"></el-button>
        </div>
        <div class="bottom-left"><!-- 西 1-->
          <Flv v-if="isReload && videos2 && videos2.length > 0" :curDevVideos="videos2" :autoPlay="autoPlay" Height="auto"></Flv>
          <el-button
            v-else
            style="z-index: 9999999;"
            class="add-btn"
            type="primary"
            icon="el-icon-plus"
            @click="onAddClick(2)"></el-button>
        </div>
        <div class="bottom-right"><!-- 南 2-->
          <Flv v-if="isReload && videos1 && videos1.length > 0" :curDevVideos="videos1" :autoPlay="autoPlay" Height="auto"></Flv>
          <el-button
            v-else
            style="z-index: 9999999;"
            class="add-btn"
            type="primary"
            icon="el-icon-plus"
            @click="onAddClick(1)"></el-button>
        </div>
        <ChannelRealtimeIntersection
          v-if="isReload"
          :devId="curDevice.agentid"
          :roadDirection="roadDirection"
          :channelRealtimeStatusData="realTimeChannelData"
        />
      </div>
      <div class="content-config" v-if="isConfigShow && zoomBiz === 'config'">
        <div class="params">
          <div class="param">
            <div class="label">
              绿闪(s):
            </div>
            <div class="value">
              <el-input-number
                :min="0"
                :max="65535"
                :precision="0"
                :step="1"
                :controls="false"
                v-model.number="manualInfo.greenflash"
                size="mini"
              ></el-input-number>
            </div>
          </div>
          <div class="param">
            <div class="label">
              持续时间(s):
            </div>
            <div class="value">
              <el-input-number
                :min="0"
                :max="65535"
                :precision="0"
                :step="1"
                :controls="false"
                v-model.number="manualInfo.duration"
                size="mini"
              ></el-input-number>
            </div>
          </div>
          <div class="param">
            <div class="label">
              黄灯(s):
            </div>
            <div class="value">
              <el-input-number
                :min="0"
                :max="65535"
                :precision="0"
                :step="1"
                :controls="false"
                v-model.number="manualInfo.yellow"
                size="mini"
              ></el-input-number>
            </div>
          </div>
          <div class="param">
            <div class="label">
              全红(s):
            </div>
            <div class="value">
              <el-input-number
                :min="0"
                :max="65535"
                :precision="0"
                :step="1"
                :controls="false"
                v-model.number="manualInfo.redclear"
                size="mini"
              ></el-input-number>
            </div>
          </div>
          <div class="param">
            <div class="label">
              最小绿(s):
            </div>
            <div class="value">
              <el-input-number
                :min="0"
                :max="65535"
                :precision="0"
                :step="1"
                :controls="false"
                v-model.number="manualInfo.mingreen"
                size="mini"
              ></el-input-number>
            </div>
          </div>
        </div>
        <div class="info">*点击路口渠化图中的方向箭头锁定特勤方向</div>
        <div class="main">
          <intersection-direction-selection
            v-if="isConfigShow && zoomBiz === 'config'"
            :agentId="curDevice.agentid"
            :roadDirection="roadDirection"
            :choosedDirection="curDevice.data.data.direction"
            :choosedPedDirection="curDevice.data.data.peddirection"
            @handleClickCrossIcon="handleClickCrossIcon" />
        </div>
        <div class="bottom">
          <template v-if="operation === 'save'">
            <el-button size="medium" type="primary" @click="onSureClick">确定</el-button>
          </template>
          <template v-else>
            <el-button size="medium" @click="onRecoverClick">恢复</el-button>
            <el-button size="medium" type="primary" @click="onExecClick">执行</el-button>
          </template>
        </div>
      </div>
      <div class="content-exec" v-if="isConfigShow && zoomBiz === 'exec'">
        <scheme-config
              ref="rightpanel"
              :realtimeStatusModalvisible="false"
              :isShowBack="false"
              :agentId="curDevice.agentid"
              :agentName="curDevice.name"
              :currModel="curDevice.mode"
              :platform="curDevice.platform"
              :statusData="patternStatus"
              :phaseList="phaseList"
              :roadDirection="roadDirection"
            />
      </div>
    </div>
    <el-dialog
      width="800px"
      title="修改路口视频"
      :close-on-click-modal="false"
      :visible.sync="innerVideosVisible"
      :z-index="30000"
      append-to-body
    >
      <el-form ref="deviceId" :model="curDevice" @submit.native.prevent>
        <el-form-item
          label="路口名称:"
          label-width="35%"
        >
          <span>{{curDevice.name}}</span>
        </el-form-item>
      </el-form>
      <el-form ref="deviceId" :model="curDevice" @submit.native.prevent v-for="(item, index) of videos" :key="index">
        <el-form-item
          label=""
          label-width="5%"
        >
          <el-input v-model="item.name" placeholder="请输入视频名称" style="width: 30%;"></el-input>
          <el-input v-model="item.url"  placeholder="请输入视频地址" style="width: 50%;"></el-input>
          <el-button type="text" @click="onUPClick(item, index)" :disabled="index === 0">
            上移
          </el-button>
          <el-button type="text" @click="onDownClick(item, index)" :disabled="index === videos.length - 1">
            下移
          </el-button>
          <el-button type="text" @click="onClearClick(index)">
            清空
          </el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetInnerVideosForm('deviceVideos')">取消</el-button>
        <el-button type="primary" @click="submitDeviceVideos()">确定</el-button>
      </div>
    </el-dialog>
    <!-- add dialog -->
    <el-dialog
      width="800px"
      title="新增路口视频"
      :close-on-click-modal="false"
      :visible.sync="innerAddVideosVisible"
      :z-index="30000"
      append-to-body
    >
      <el-form ref="deviceId" :model="curDevice" @submit.native.prevent>
        <el-form-item
          label="路口名称:"
          label-width="25%"
        >
          <span>{{curDevice.name}}</span>
        </el-form-item>
      </el-form>
      <el-form ref="deviceId" :model="curDevice" @submit.native.prevent>
        <el-form-item
          label="视频名称:"
          label-width="25%"
        >
        <el-input v-model="curVideo.name" placeholder="请输入视频名称" style="width: 100%;"></el-input>
        </el-form-item>
      </el-form>
      <el-form ref="deviceId" :model="curDevice" @submit.native.prevent>
        <el-form-item
          label="视频地址:"
          label-width="25%"
        >
        <el-input v-model="curVideo.url" placeholder="请输入视频地址" style="width: 100%;"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetInnerVideosForm('deviceVideos')">取消</el-button>
        <el-button type="primary" @click="submitAddDeviceVideos()">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Flv from '../Flvjs/index.vue'
// @ts-ignore
import { putControl, getTscControl } from '../../api/control'
import { getRealTimeChannel } from '../../api/param'
import { getMessageByCode } from '../../utils/responseMessage'
import { getTscPhase } from '../../api/route'// getPatternList
import { mapState } from 'vuex'
import { GetRouteVideos, UpdateRouteVideos } from '../../api/deviceVideo'
import { ExecuteViproute } from '../../api/service'
import ChannelRealtimeIntersection from '@/components/ChannelRealtimeIntersection/index.js';
export default {
  name: 'TerminalLock',
  components: {
    Flv,
    ChannelRealtimeIntersection
  },
  props: {
    curDevice: {
      type: Object,
      default: () => {}
    },
    preDevice: {
      type: Object,
      default: () => null
    },
    nextDevice: {
      type: Object,
      default: () => null
    },
    isConfigShow: {
      type: Boolean,
      defaultValue: true
    },
    operation: {
      type: String,
      default: 'config' // save
    },
    zoomBiz: {
      type: String,
      default: 'config' // config, play
    }
  },
  computed: {
    // @ts-ignore
    ...mapState({
      roadDirection: state => state.globalParam.roadDirection
    }),
    func () {
      let res = getRealTimeChannel
      if (this.curDevice && this.curDevice.platform && this.curDevice.platform !== '' && this.curDevice.platform !== 'OpenATC') {
        res = getTscControl
      }
      return res
    },
    isShowVideo () {
      // let res = this.curDevice && this.curDevice.videos && this.curDevice.videos.length > 0
      let res = this.curDevice
      return res
    },
    videos0 () {
      let res = this.getVideoItemByIndex(0)
      return res
    },
    videos1 () {
      let res = this.getVideoItemByIndex(1)
      return res
    },
    videos2 () {
      let res = this.getVideoItemByIndex(2)
      return res
    },
    videos3 () {
      let res = this.getVideoItemByIndex(3)
      return res
    }
  },
  watch: {
    curDevice: {
      handler: function (val, oldVal) {
        if (val) {
          this.manualInfo = val.data.data
          this.isReload = false
          setTimeout(() => {
            this.isReload = true
          }, 0.1 * 1000)
        }
      },
      deep: true
    },
    async isConfigShow (val) {
      if (val) {
        if (this.zoomBiz === 'exec') {
          const id = this.curDevice.agentid
          await this.getCurPhase(id)
          await this.getCurrentPatternStatus(id)
        } else if (this.zoomBiz === 'play') {
          if (!this.curDevice || !this.curDevice.videos || this.curDevice.videos.length === 0) {
            if (this.switchBiz === 'terminal') {
              // this.initData()
            } else {
              this.switchBiz = 'terminal'
            }
          } else {
            this.switchBiz = 'terminal'
          }
          this.initData()
        }
      } else {
        this.clearChannelInterval()
      }
    },
    switchBiz (val) {
      if (val === 'terminal') {
        this.initData()
      } else {
        this.clearChannelInterval()
      }
    }
  },
  mounted () {
  },
  data () {
    return {
      editIndex: 0,
      innerVideosVisible: false,
      innerAddVideosVisible: false,
      videos: [],
      curVideo: {
        id: '',
        name: '',
        url: ''
      },
      defaultVideo: [
        {
          id: '',
          name: this.$t('openatc.devicemanager.video') + '-1',
          url: ''
        },
        {
          id: '',
          name: this.$t('openatc.devicemanager.video') + '-2',
          url: ''
        },
        {
          id: '',
          name: this.$t('openatc.devicemanager.video') + '-3',
          url: ''
        },
        {
          id: '',
          name: this.$t('openatc.devicemanager.video') + '-4',
          url: ''
        }
      ],
      isReload: true,
      isfromatc: true,
      phaseList: [], // 相位列表
      patternStatus: {}, // 方案面板数据
      lockPhaseBtnName: this.$t('openatccomponents.overview.implement'),
      realTimeChannelTimer: null,
      realTimeChannelData: {},
      switchBiz: '',
      manualInfo: {},
      isShow: true,
      // isConfigShow: false,
      autoPlay: true
    }
  },
  methods: {
    getIntersectionName (dev) {
      let res
      if (dev) {
        res = dev && dev.name
      }
      if (res && res.length > 10) {
        res = res.slice(0, 5) + '...' + res.slice(-5)
      }
      return res
    },
    getVideoItemByIndex (index) {
      let res = []
      if (this.curDevice && this.curDevice.videos && this.curDevice.videos.length > index) {
        let video = this.curDevice.videos[index]
        let videoUrl = video && video.url
        // if (videoUrl && videoUrl.endsWith('.flv')) {
        if (videoUrl) {
          let targetVideo = {
            id: index,
            name: video.name,
            url: video.url,
            isFlv: true
          }
          res = [targetVideo]
        }
      }
      return res
    },
    onUPClick (item, index) {
      if (index === 0) {
        return false
      } else {
        let targetIndex = index - 1
        this.videos.splice(index, 1)
        this.videos.splice(targetIndex, 0, item)
      }
    },
    onDownClick (item, index) {
      if (index === this.videos.length - 1) {
        return false
      } else {
        let targetIndex = index + 1
        this.videos.splice(index, 1)
        this.videos.splice(targetIndex, 0, item)
      }
    },
    onClearClick (index) {
      let targetItem = JSON.parse(JSON.stringify(this.defaultVideo[index]))
      this.videos.splice(index, 1, targetItem)
    },
    async submitAddDeviceVideos () {
      this.videos.splice(this.editIndex, 1, this.curVideo)
      this.submitDeviceVideos()
    },
    async submitDeviceVideos () {
      let list = this.videos.map(item => {
          let rec = {
            agentid: this.curDevice.agentid,
            name: item.name,
            url: item.url
          }
          return rec
        }
      )
      this.updateDeviceVideos(list, this.curDevice.agentid)
    },
    resetInnerVideosForm () {
      // 内层表单重置
      this.innerVideosVisible = false
      this.innerAddVideosVisible = false
      this.curVideo = {
        id: '',
        name: '',
        url: ''
      }
    },
    onAddClick (index, item) {
      this.editIndex = index
      this.preareVideos()
      this.curVideo = this.videos[index]
      this.innerAddVideosVisible = true
    },
    preareVideos () {
      let videos = JSON.parse(JSON.stringify(this.curDevice.videos))
      // set 4 only
      if (!videos || videos.length < 4) {
        videos = [...videos, ...JSON.parse(JSON.stringify(this.defaultVideo))]
      }
      videos = videos.slice(0, 4)
      this.videos = videos
    },
    async onModifyVideosClick () {
      // let ids = [this.curDevice.agentid]
      // let videos = await this.getRouteVideos(ids)
      this.preareVideos()
      this.innerVideosVisible = true
    },
    getRouteVideos (deviceIds) {
      let _this = this
      // @ts-ignore
      return new Promise((resolve, reject) => {
        // @ts-ignore
        GetRouteVideos(deviceIds).then(res => {
          if (!res.data.success) {
            _this.$message.error(getMessageByCode(res.data.code, _this.$i18n.locale))
            return
          }
          let videos = res.data.data
          resolve(videos)
        })
      })
    },
    updateDeviceVideos (videos, id) {
      // let _this = this
      let data = videos
      UpdateRouteVideos(data, id).then(res => {
        if (!res.data.success) {
          this.$message.error(getMessageByCode(res.data.code, this.$i18n.locale))
          return
        }
        this.innerVideosVisible = false
        this.dialogFormVisible = false
        this.innerAddVideosVisible = false
        this.$message({
          message: this.$t('openatc.common.updatesuccess'),
          type: 'success',
          duration: 1 * 1000,
          onClose: () => {
            // _this.$parent.onPlayClick(this.curDevice)
          }
        })
        this.curDevice.videos = JSON.parse(JSON.stringify(videos))
      })
    },
    reloadVideos () {
      let videoReloadDoms = document.getElementsByClassName('icon-shipinshuaxin')
      for (let dom of videoReloadDoms) {
        // console.log('auto reload video:', dom)
        dom && dom.click()
      }
    },
    getCurPhase (agentid) {
      // 获取当前上行/下行相位选项
      this.loading = true
      return new Promise((resolve, reject) => {
        getTscPhase(agentid).then(res => {
          this.loading = false
          if (!res.data.success) {
            let msg = getMessageByCode(res.data.code, this.$i18n.locale)
            if (res.data.data) {
              let errorCode = res.data.data.errorCode
              if (errorCode) {
                msg = msg + ' - ' + getMessageByCode(errorCode, this.$i18n.locale)
              }
            }
            this.$message.error(msg)
            return
          }
          this.phaseList = res.data.data.data.phaseList
          resolve(this.phaseList)
        })
      })
    },
    async getCurrentPatternStatus (iframdevid) {
      await getTscControl(iframdevid).then((data) => {
        if (!data.data.success) {
          if (data.data.code === '4003') {
            this.$message.error(getMessageByCode(data.data.code, this.$i18n.locale))
            return
          }
          let parrenterror = getMessageByCode(data.data.code, this.$i18n.locale)
          if (data.data.data) {
            // 子类型错误
            let childErrorCode = data.data.data.errorCode
            if (childErrorCode) {
              let childerror = getMessageByCode(data.data.data.errorCode, this.$i18n.locale)
              this.$message.error(parrenterror + ',' + childerror)
            }
          } else {
            this.$message.error(parrenterror)
          }
          return
        }
        this.patternStatus = JSON.parse(JSON.stringify(data.data.data.data))
      }).catch(error => {
        console.log(error)
      })
    },
    getBizClass (biz) {
      let className = ''
      if (this.switchBiz === biz) {
        className = 'btn-current'
      } else {
        className = 'btn-gray'
      }
      // if (biz === 'video' && (!this.curDevice || !this.curDevice.videos || this.curDevice.videos.length === 0)) {
      //   className = 'btn-gray'
      // }
      return className
    },
    onSwitchBizClick (biz) {
      this.switchBiz = biz
    },
    getExecClass () {
      let res = 'btn-current'
      return res
    },
    onSwitchIntersectionBtnClick (dev) {
      this.$emit('onSwitchIntersectionBtnClick', dev)
    },
    onaExecBtnClick (operation) {
      this.executeViproute(this.curDevice, operation)
    },
    executeViproute (node, operation) {
      let reqData = {
        'agentid': node.agentid,
        'viprouteid': node.viprouteid,
        'operation': operation
      }
      ExecuteViproute(reqData).then(res => {
        // this.isBtnDisabled = false
        if (!res.data.success) {
          let msg = getMessageByCode(res.data.code, this.$i18n.locale)
          if (res.data.data) {
            let errorCode = res.data.data.errorCode
            if (errorCode) {
              msg = msg + ' - ' + getMessageByCode(errorCode, this.$i18n.locale)
            }
          }
          this.$message.error(msg)
          return false
        } else {
          if (res.data.data && res.data.data.data) {
            let success = res.data.data.data.success
            if (success !== 0) {
              // 手动面板控制提示
              let errormsg = 'openatccomponents.overview.putTscControlError' + success
              this.$message.error(this.$t(errormsg))
              return false
            }
          }
          this.$message.success(this.$t('openatc.common.operationsuccess'))
        }
        this.$emit('research')
      })
    },
    initData () {
      if (this.realTimeChannelTimer) {
        this.clearChannelInterval()
      }
      // this.realTimeChannelTimer = setInterval(() => {
      //   this.getChannel(this.curDevice.agentid)
      // }, 1000)
      this.getChannel(this.curDevice.agentid)
      // reload videos
      if (this.isShowVideo) {
        let reloadTimer = setTimeout(() => {
          clearTimeout(reloadTimer)
          this.reloadVideos()
        }, 2 * 1000)
      }
    },
    clearChannelInterval () {
      if (this.realTimeChannelTimer !== null) {
        clearInterval(this.realTimeChannelTimer) // 清除定时器
        this.realTimeChannelTimer = null
      }
    },
    onLeftCloseClick () {
      // this.isConfigShow = false
      this.$emit('onCloseClick')
    },
    onRecoverClick () {
      const recoverData = {
        control: 0,
        terminal: 0,
        value: 101,
        delay: 0,
        duration: 300
      }
      const reqData = {
        param: recoverData,
        agentid: this.curDevice.agentid
      }
      this.putControl(reqData)
    },
    onExecClick () {
      const spData = JSON.parse(JSON.stringify(this.curDevice.data))
      delete spData.data.iconList
      const reqData = {
        param: spData,
        agentid: this.curDevice.agentid
      }
      if (!this.curDevice.data.data.direction || this.curDevice.data.data.direction.length === 0) {
        this.$message.error(this.$t('openatccomponents.overview.directionnull'))
        return
      }
      this.putControl(reqData)
    },
    async putControl (reqData) {
      let message = ''
      let data = await putControl(reqData)
      let success = 0
      if (!data.data.success) {
        message = getMessageByCode(data.data.code, this.$i18n.locale)
      } else {
        if (data.data.data && data.data.data.data) {
          success = data.data.data.data.success
          if (success !== 0) {
            let errormsg = 'openatccomponents.overview.putTscControlError' + success
            message = errormsg
          }
        }
      }
      if (message === '') {
        this.$message.success(this.$t('openatccomponents.common.download'))
      } else {
        this.$message.error(message)
      }
      return message
    },
    onSureClick () {
      this.$emit('onSaveClick', this.manualInfo)
      this.isConfigShow = false
    },
    handleClickCrossIcon (allChoosedDir, curClickedPhase) {
      this.curDevice.data.data.direction = allChoosedDir.direction
      this.curDevice.data.data.peddirection = allChoosedDir.peddirection
    },
    onCloseClick () {
      this.isShow = false
    },
    show () {
      this.isShow = true
    },
    getChannel (agentId) {
      let funct = this.func
      funct(agentId).then(data => {
        let res = data.data
        if (!res.success) {
          this.$message.error(getMessageByCode(data.data.code, this.$i18n.locale))
          return
        }
        this.realTimeChannelData = res.data.data
      }).catch(error => {
        this.$message.error(error)
        console.log('getChannel err:', error)
      })
    }
  },
  destroyed () {
    this.clearChannelInterval()
    this.isConfigShow = false
  }
}
</script>
<style scoped></style>
