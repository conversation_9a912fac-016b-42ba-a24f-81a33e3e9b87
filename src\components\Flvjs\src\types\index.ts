// Flvjs组件相关类型定义

export interface VideoInfo {
  id: string | number;
  name: string;
  url: string;
  videoElement?: HTMLVideoElement;
  flvPlayer?: any;
}

export interface PlayOptions {
  type: number;
  url: string;
  activeSei?: boolean;
  seiCanvasContainer?: string;
}

export interface WebRTCOptions {
  element: HTMLVideoElement;
  debug: boolean;
  zlmsdpUrl: string;
  simulcast: boolean;
  useCamera: boolean;
  audioEnable: boolean;
  videoEnable: boolean;
  recvOnly: boolean;
  resolution: {
    w: number;
    h: number;
  };
  usedatachannel: boolean;
}

export interface FlvPlayerProps {
  curVideoInfo?: VideoInfo;
  Width?: string;
  Height?: string;
  Title?: string;
  autoPlay?: boolean;
}

export interface FlvProps {
  curDevVideos: VideoInfo[];
  Width?: string;
  Height?: string;
  Title?: string;
  autoPlay?: boolean;
}

// WebRTC播放器接口
export interface IWebrtcPlayer {
  zml: any;
  _domId: string;
  options?: any;
  play(domID: string, opt: PlayOptions): void;
  unPlay(): void;
}

// Hook返回类型
export interface UsePlayerReturn {
  replay: (id: string, playOpt: PlayOptions) => void;
  webrtcPlayer: IWebrtcPlayer | null;
}

// FLV播放器配置
export interface FlvPlayerConfig {
  type: string;
  isLive: boolean;
  url: string;
}

export interface FlvPlayerOptions {
  stashInitialSize: number;
}

// 组件事件类型
export interface FlvPlayerEmits {
  resetComponent: (nextmediaid: string | number) => void;
}

export interface FlvEmits {
  // 可以根据需要添加事件类型
}
