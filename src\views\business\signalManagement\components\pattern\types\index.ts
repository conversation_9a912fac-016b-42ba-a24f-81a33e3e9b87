export interface Phase {
  id: number
  name: string
  ring: number
  desc?: string
}

export interface Stage {
  key: number
  stageNo: number
  green: number
  yellow: number
  red: number
  min: number
  max: number
  phases: number[]
  stageSplit: number
}

export interface Pattern {
  id: number
  desc: string
  offset: number
  cycle: number
  contrloType?: 'ring' | 'stage'
  stagesList: Stage[]
  rings: Phase[][]
  options?: any
  forbiddenstage?: string
  screenstage?: string
  coordinatestage?: string
}

export interface PatternStore {
  patternList: Pattern[]
  phaseList: Phase[]
  ringCount: number
  isStageMode: boolean
}