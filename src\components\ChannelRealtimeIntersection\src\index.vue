<!--通道实时状态路口图-->
<template>
  <div class="channel-realtime-intersection">
    <IntersectionMap
      v-if="reset"
      ref="intersectionMapRef"
      channelType
      :crossStatusData="crossStatusData"
      :devId="devId"
      :graphicMode="true"
      :roadDirection="roadDirection"
      :isThirdSignal="isThirdSignal"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, watch, nextTick, onMounted, type PropType } from 'vue';
import { ElMessage } from 'element-plus';
import IntersectionMap from '@/components/IntersectionMap';
import { getMessageByCode } from '@/components/IntersectionMap/crossDirection/utils';
import { getSignalManagement } from '@/api/business/signalManagement';
import type { CrossStatusData } from '@/components/IntersectionMap/crossDirection/types';

export default defineComponent({
  name: 'ChannelRealtimeIntersection',
  components: {
    IntersectionMap
  },
  props: {
    devId: {
      type: Number,
      default: 0
    },
    roadDirection: {
      type: String as PropType<'right' | 'left'>,
      default: 'right'
    },
    channelRealtimeStatusData: {
      type: Object as PropType<CrossStatusData>
    }
  },
  setup(props) {
    // 响应式数据
    const reset = ref<boolean>(true);
    const crossStatusData = reactive<CrossStatusData>({});
    const isThirdSignal = ref<boolean>(false);
    const platform = ref<string>('');

    // 模板引用
    const intersectionMapRef = ref();

    // 获取平台信息
    const getPlatform = async (): Promise<void> => {
      try {
        const res = await getSignalManagement(props.devId);
        if (!res.data.success) {
          const commomMsg = '信号机ID: ' + props.devId;
          ElMessage.error(getMessageByCode(res.data.code) + ' - ' + commomMsg);
          return;
        }

        platform.value = res.data.data.platform || '';
        if (platform.value !== '' && platform.value !== 'DearAI') {
          isThirdSignal.value = true;
        } else {
          isThirdSignal.value = false;
        }
        // 注意：原始代码中有 thirdSignal 属性的检查，但在当前 props 中未定义
        // 如果需要支持 thirdSignal prop，请在 props 中添加该属性
      } catch (error) {
        console.error('获取平台信息失败:', error);
      }
    };

    // 监听器
    watch(
      () => props.devId,
      (val: number) => {
        if (val) {
          reset.value = false;
          nextTick(() => {
            reset.value = true;
          });
        }
      },
      { deep: true }
    );

    watch(
      () => props.channelRealtimeStatusData,
      (val?: CrossStatusData) => {
        if (val) {
          // 相位统计数据数据
          Object.assign(crossStatusData, JSON.parse(JSON.stringify(val)));
        }
      },
      { deep: true }
    );

    // 生命周期
    onMounted(() => {
      getPlatform();
      if (props.channelRealtimeStatusData) {
        Object.assign(crossStatusData, JSON.parse(JSON.stringify(props.channelRealtimeStatusData)));
      }
    });

    return {
      reset,
      crossStatusData,
      isThirdSignal,
      platform,
      intersectionMapRef,
      getPlatform
    };
  }
});
</script>

<style lang="scss" scoped>
.channel-realtime-intersection {
  // 组件样式
}
</style>
