export interface SignalManagementVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 信号机名称
   */
  signalName: string;

  /**
   * 信号机IP
   */
  signalIp: string;

  /**
   * 信号机端口
   */
  signalPort: number;

  /**
   * 信号机协议
   */
  signalProtocol: string;

  /**
   * 信号机厂家
   */
  signalVender: string;

  /**
   * 信号机配置
   */
  signalConfig: object;

  /**
   * 信号机数据
   */
  data: object;

  /**
   * 信号机环
   */
  ring: object;

  /**
   * 信号机指令数据
   */
  commondata: object;

  /**
   * 信号机合并方向
   */
  mergedirection: object;

}

export interface SignalManagementForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 信号机名称
   */
  signalName?: string;

  /**
   * 信号机IP
   */
  signalIp?: string;

  /**
   * 信号机端口
   */
  signalPort?: number;

  /**
   * 信号机协议
   */
  signalProtocol?: string;

  /**
   * 信号机厂家
   */
  signalVender?: string;

  /**
   * 信号机配置
   */
  signalConfig?: object;

  /**
   * 信号机数据
   */
  data?: object;

  /**
   * 信号机环
   */
  ring?: object;

  /**
   * 信号机指令数据
   */
  commondata?: object;

  /**
   * 信号机合并方向
   */
  mergedirection?: object;

}

export interface SignalManagementQuery extends PageQuery {

  /**
   * 信号机名称
   */
  signalName?: string;

  /**
   * 信号机IP
   */
  signalIp?: string;

  /**
   * 信号机协议
   */
  signalProtocol?: string;

  /**
   * 信号机厂家
   */
  signalVender?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

export interface MessageData extends BaseEntity {
  /**
   * 设备ID
   */
  devid: number;

  /**
   * 信号机上报的ID
   */
  thirdpartyid?: string;

  /**
   * 操作类型
   */
  operation?: string;

  /**
   * 信息类型
   */
  infotype?: string;

  /**
   * 设备模式 - 平台设备还是直连设备，ocp：直连，scp：平台
   */
  model?: string;

  /**
   * 创建时间
   */
  createtime?: string;

  /**
   * 系统时间
   */
  systime?: string;

  /**
   * 状态时间戳
   */
  sts?: number;

  /**
   * 数据对象
   */
  data?: Record<string, any>;

  /**
   * 延迟时间
   */
  delay?: number;

  /**
   * 数据源
   */
  source?: string;

  /**
   * 认证类型：0：默认，1：CRC，2：密钥
   */
  auth?: number;

  /**
   * 认证密钥
   */
  secretkey?: string;
}


