import PhaseDataModel from '@/components/PhaseDataModel';
import CrossDiagramMgr from '@/components/EdgeMgr/controller/crossDiagramMgr';

// 类型定义
interface PhaseItem {
  id: number;
  ring: number;
  direction: number[];
  peddirection?: number[];
  controltype?: number;
  type?: number;
  mode?: number;
}

interface CrossStatusData {
  phase?: PhaseStatusItem[];
  stages?: number[][];
  current_stage?: number;
}

interface PhaseStatusItem {
  id: number;
  type?: number;
  mode?: number;
  locktype?: number;
  close?: number;
}

interface BusPhaseData {
  phaseid: number;
  id: number;
  name: string;
  controltype: number;
  key?: string;
}

interface SidewalkPhaseData {
  key: string;
  phaseid: number;
  id: number;
  name: string;
}

interface PedDirection {
  name: string;
  id: number;
}

interface PhaseDescription {
  id: number;
  peddirection: PedDirection[];
  color: string;
}

interface PhaseRing {
  num: number;
  phases: (PhaseItem & {
    name: string;
    desc: PhaseDescription[];
    locktype: number;
    close: number;
  })[];
}

interface StageItem {
  id: number;
  phaseid?: string | number;
  color: string;
  controltype: number;
  peddirection: PedDirection[];
  sidewalkPhaseData: SidewalkPhaseData[];
  type?: number;
}

/**
 * 环数据模型类
 */
export default class RingDataModel {
  private crossStatusData: CrossStatusData;
  private phaseList: PhaseItem[];
  private busPhaseData: BusPhaseData[];
  private PhaseDataModel: PhaseDataModel;
  private CrossDiagramMgr: CrossDiagramMgr;
  private sidewalkPhaseData: SidewalkPhaseData[];
  private stagesList: StageItem[][];
  private List: StageItem[][];

  /**
   * 构造函数
   * @param crossStatusData 交叉口状态数据
   * @param phaseList 相位列表
   * @param busPhaseData 公交相位数据
   */
  constructor(
    crossStatusData: CrossStatusData = {},
    phaseList: PhaseItem[],
    busPhaseData: BusPhaseData[] = []
  ) {
    this.crossStatusData = crossStatusData;
    this.phaseList = phaseList;
    this.busPhaseData = busPhaseData;
    this.PhaseDataModel = new PhaseDataModel();
    this.CrossDiagramMgr = new CrossDiagramMgr();
    this.sidewalkPhaseData = [];
    this.stagesList = [];
    this.List = [];
  }

  /**
   * 初始化环相位数据
   * @returns 相位环数据
   */
  initRingPhaseData(): PhaseRing[] {
    // 环信息从单独上载相位信息里获取，以免相位锁定后，方案状态数据里没有rings，导致相位锁定控制列表无法显示
    const phaseRings: PhaseRing[] = [];
    const map: Record<number, PhaseItem> = {};
    const dest: PhaseRing[] = [];

    for (let i = 0; i < this.phaseList.length; i++) {
      const ai = this.phaseList[i];
      if (!map[ai.ring]) {
        const addphse = this.addPhaseInfo(ai);
        dest.push({
          num: ai.ring,
          phases: [{ ...ai, ...addphse }]
        });
        map[ai.ring] = ai;
      } else {
        for (let j = 0; j < dest.length; j++) {
          const dj = dest[j];
          if (dj.num === ai.ring) {
            const addphse = this.addPhaseInfo(ai);
            dj.phases.push({ ...ai, ...addphse });
            break;
          }
        }
      }
    }

    return JSON.parse(JSON.stringify(dest));
  }

  /**
   * 添加相位信息
   * @param phase 相位数据
   * @returns 附加的相位信息
   */
  private addPhaseInfo(phase: PhaseItem): {
    name: string;
    desc: PhaseDescription[];
    locktype: number;
    close: number;
  } {
    let addphse: any = {};
    addphse.name = '相位' + phase.id;
    addphse.desc = this.getPhaseDescription(phase);
    // 相位锁定选项默认都按照解锁状态显示
    addphse.locktype = 0;
    addphse.close = 0;

    if (this.crossStatusData !== null && this.crossStatusData.phase) {
      // 如果方案状态相位有close字段，这边就需要对应close状态进相位关断控制的选项里
      const phaseStatus = this.crossStatusData.phase.filter(ele => ele.id === phase.id)[0];
      if (phaseStatus) {
        addphse = { ...addphse, ...phaseStatus };
      }
    }

    return addphse;
  }

  /**
   * 获取相位描述
   * @param phaseItem 相位项
   * @returns 相位描述列表
   */
  private getPhaseDescription(phaseItem: PhaseItem): PhaseDescription[] {
    const list: PhaseDescription[] = [];
    let peddirections: PedDirection[] = [];
    const sidewalkPhaseData = this.getPedPhasePos();

    for (const walk of sidewalkPhaseData) {
      if (phaseItem.peddirection) {
        for (const ped of phaseItem.peddirection) {
          const obj: PedDirection = {
            name: walk.name,
            id: walk.id
          };
          if (ped === walk.id) {
            peddirections.push(obj);
            peddirections = Array.from(new Set(peddirections));
          }
        }
      } else {
        peddirections = [];
      }
    }

    for (const id of phaseItem.direction) {
      const obj: PhaseDescription = {
        id: id,
        peddirection: peddirections,
        color: '#454545'
      };
      list.push(obj);
    }

    return list;
  }

  /**
   * 获取行人相位位置信息
   * @returns 行人相位数据
   */
  getPedPhasePos(): SidewalkPhaseData[] {
    // 行人相位信息
    this.sidewalkPhaseData = [];
    this.phaseList.forEach((ele, i) => {
      if (ele.peddirection) {
        ele.peddirection.forEach((dir, index) => {
          // 行人相位
          const sidePos = this.PhaseDataModel.getSidePos(dir);
          if (sidePos) {
            this.sidewalkPhaseData.push({
              key: this.CrossDiagramMgr.getUniqueKey('pedphase'),
              phaseid: ele.id, // 相位id，用于对应相位状态
              id: dir,
              name: sidePos.name
            });
          }
        });
      }
    });

    return this.sidewalkPhaseData;
  }

  /**
   * 获取公交位置信息
   * @returns 公交相位数据
   */
  getBusPos(): BusPhaseData[] {
    // 公交相位信息
    this.busPhaseData = [];
    this.phaseList.forEach((ele, i) => {
      if (ele.controltype) {
        ele.direction.forEach((dir, index) => {
          // 车道相位
          const busPhasePos = this.PhaseDataModel.getBusPhasePos(dir);
          if (busPhasePos) {
            this.busPhaseData.push({
              phaseid: ele.id, // 相位id，用于对应相位状态
              id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
              name: busPhasePos.name,
              controltype: ele.controltype
            });
          }
        });
      }
    });

    const result: BusPhaseData[] = [];
    const obj: Record<number, boolean> = {};
    for (let i = 0; i < this.busPhaseData.length; i++) {
      if (!obj[this.busPhaseData[i].phaseid]) {
        result.push(this.busPhaseData[i]);
        obj[this.busPhaseData[i].phaseid] = true;
      }
    }

    this.busPhaseData = result;
    return this.busPhaseData;
  }

  /**
   * 获取锁定数据
   * @returns 锁定数据列表
   */
  getlockData(): StageItem[][] {
    const stagesTemp: StageItem[][] = [];
    const sidewalkPhaseData = this.getPedPhasePos();

    for (const phase of this.phaseList) {
      const tempList: StageItem[] = [];
      const directionList = phase.direction;
      let peddirections: PedDirection[] = [];

      if (directionList && phase.peddirection) {
        for (const walk of sidewalkPhaseData) {
          for (const ped of phase.peddirection) {
            const obj: PedDirection = {
              name: walk.name,
              id: walk.id
            };
            if (ped === walk.id) {
              peddirections.push(obj);
              peddirections = Array.from(new Set(peddirections));
            }
          }
        }
      } else {
        peddirections = [];
      }

      const mappedList = directionList.map(dir => ({
        id: dir,
        color: '#606266',
        controltype: phase.controltype || 0,
        peddirection: peddirections,
        sidewalkPhaseData: sidewalkPhaseData
      }));

      stagesTemp.push(mappedList);
    }

    return stagesTemp;
  }
  /**
   * 获取阶段数据
   * @param datatype 数据类型
   * @returns 阶段列表
   */
  getStageData(datatype?: any): StageItem[][] {
    const data: CrossStatusData = this.crossStatusData;
    const stagesTemp: StageItem[][] = [];
    const busPhaseData: BusPhaseData[] = this.getBusPos();
    const sidewalkPhaseData: SidewalkPhaseData[] = this.getPedPhasePos();

    // 按阶段数据处理
    const stages: number[][] = data.stages || [];

    for (const stage of stages) {
      let tempList: StageItem[] = [];
      let directionList: number[] = [];
      let currPhaseid: string = '';
      let stageControType: number = 0;
      let peddirections: PedDirection[] = [];
      let phasetype: number | undefined;

      for (const stg of stage) {
        const phaseMode: PhaseStatusItem[] = (data.phase || []).filter(item => item.id === stg);
        phasetype = phaseMode[0]?.type;

        const currPhase: PhaseItem | undefined = this.phaseList.find((item) => {
          return item.id === stg;
        });

        if (!currPhase) continue;

        if (currPhase !== undefined && phaseMode[0]?.mode !== 1) {
          directionList = [...currPhase.direction, ...directionList];
        }

        if (currPhase.peddirection) {
          for (const walk of sidewalkPhaseData) {
            for (const ped of currPhase.peddirection) {
              const obj: PedDirection = {
                name: walk.name,
                id: walk.id
              };

              if (ped === walk.id) {
                peddirections.push(obj);
                // 去重处理
                const uniquePeddirections = peddirections.filter((item, index, self) =>
                  index === self.findIndex(t => t.id === item.id)
                );
                peddirections = uniquePeddirections;
              }
            }
          }
        } else {
          peddirections = [];
        }

        for (const busPhase of busPhaseData) {
          if (stg === busPhase.phaseid) {
            stageControType = busPhase.controltype;
          }
        }
      }

      // 去重处理
      directionList = [...new Set(directionList)];

      tempList = directionList.map((dir: number): StageItem => {
        const temp: StageItem = {
          id: dir,
          phaseid: currPhaseid,
          color: '#606266',
          controltype: stageControType,
          peddirection: peddirections,
          sidewalkPhaseData: sidewalkPhaseData,
          type: phasetype
        };
        return temp;
      });

      if (directionList.length === 0) {
        tempList = [
          {
            id: -1,
            color: '#606266',
            controltype: stageControType,
            peddirection: peddirections,
            sidewalkPhaseData: sidewalkPhaseData,
            type: phasetype
          }
        ];
      }

      stagesTemp.push(tempList);
    }

    this.stagesList = JSON.parse(JSON.stringify(stagesTemp));
    return this.stagesList;
  }
  getPhaseLegendData () {
    let data = this.crossStatusData
    let stagesTemp = []
    let busPhaseData = this.getBusPos()
    let sidewalkPhaseData = this.getPedPhasePos()
    let phaseIdList
    phaseIdList = data.phase.map(ele => [ele.id])
    for (let phase of phaseIdList) {
      let tempList = []
      let directionList = []
      let currPhaseid = ''
      let stageControType = 0
      let peddirections = []
      for (let id of phase) {
        let phaseMode = data.phase.filter(item => item.id === id)
        let currPhase = this.phaseList.filter((item) => {
          return item.id === id
        })[0]
        if (!currPhase) return
        if (currPhase !== undefined && phaseMode[0].mode !== 1) {
          directionList = [...currPhase.direction, ...directionList]
          currPhaseid = id
        }
        if (currPhase.peddirection) {
          for (let walk of sidewalkPhaseData) {
            for (let ped of currPhase.peddirection) {
              const obj: PedDirection = {
                name: walk.name,
                id: walk.id
              };
              if (ped === walk.id) {
                peddirections.push(obj)
                peddirections = Array.from(new Set(peddirections))
              }
            }
          }
        } else {
          peddirections = []
        }
        for (let busPhase of busPhaseData) {
          if (id === busPhase.phaseid) {
            stageControType = busPhase.controltype
          }
        }
      }
      directionList = [...new Set(directionList)]
      tempList = directionList.map(dir => {
        let temp = {
          id: dir,
          phaseid: currPhaseid,
          color: '#606266',
          controltype: stageControType,
          peddirection: peddirections,
          sidewalkPhaseData: sidewalkPhaseData
        }
        return temp
      })
      if (directionList.length === 0) {
        tempList = [
          {
            id: -1,
            phaseid: currPhaseid,
            color: '#606266',
            controltype: stageControType,
            peddirection: peddirections,
            sidewalkPhaseData: sidewalkPhaseData
          }
        ]
      }
      stagesTemp.push(tempList)
    }
    this.List = JSON.parse(JSON.stringify(stagesTemp))
    return this.List
  }
}
