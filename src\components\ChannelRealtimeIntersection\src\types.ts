// 通道实时状态路口图组件类型定义

import type { CrossStatusData } from '@/components/IntersectionMap/crossDirection/types';

/**
 * 组件 Props 接口
 */
export interface ChannelRealtimeIntersectionProps {
  /** 设备ID */
  devId: number;
  /** 道路行车方向 */
  roadDirection: 'right' | 'left';
  /** 通道实时状态数据 */
  channelRealtimeStatusData?: CrossStatusData;
}

/**
 * 组件数据接口
 */
export interface ChannelRealtimeIntersectionData {
  /** 重置标志 */
  reset: boolean;
  /** 路口状态数据 */
  crossStatusData: CrossStatusData;
  /** 是否为第三方信号机 */
  isThirdSignal: boolean;
  /** 平台类型 */
  platform?: string;
}

/**
 * API 响应接口
 */
export interface ApiResponse<T = any> {
  success: boolean;
  code: string | number;
  data: T;
  message?: string;
}

/**
 * 信号机管理响应数据
 */
export interface SignalManagementData {
  platform: string;
  [key: string]: any;
}
