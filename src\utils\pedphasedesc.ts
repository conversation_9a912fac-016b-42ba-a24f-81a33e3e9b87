// 行人相位描述接口
export interface PedestrianPhaseDescription {
  id: number;
  name: string;
}

// 行人相位图标接口
export interface PedestrianPhaseIcon {
  id: number;
  name: string;
  img: string;
}

// 行人相位描述数据
const PED_PHASE_DESCRIPTIONS: PedestrianPhaseDescription[] = [
    {
      id: 1,
      name: '东行人'
    },
    {
      id: 2,
      name: '西行人'
    },
    {
      id: 3,
      name: '南行人'
    },
    {
      id: 4,
      name: '北行人'
    },
    {
      id: 5,
      name: '东行人-上'
    },
    {
      id: 6,
      name: '东行人-下'
    },
    {
      id: 7,
      name: '西行人-上'
    },
    {
      id: 8,
      name: '西行人-下'
    },
    {
      id: 9,
      name: '南行人-左'
    },
    {
      id: 10,
      name: '南行人-右'
    },
    {
      id: 11,
      name: '北行人-左'
    },
    {
      id: 12,
      name: '北行人-右'
    },
    {
      id: 13,
      name: 'X行人-/'
    },
    {
      id: 14,
      name: 'X行人-\\'
    },
    {
      id: 15,
      name: '东西路段行人'
    },
    {
      id: 16,
      name: '南北路段行人'
    },
    {
      id: 17,
      name: '东南行人'
    },
    {
      id: 18,
      name: '西南行人'
    },
    {
      id: 19,
      name: '东北行人'
    },
    {
      id: 20,
      name: '西北行人'
    },
    {
      id: 21,
      name: '东南行人-上'
    },
    {
      id: 22,
      name: '东南行人-下'
    },
    {
      id: 23,
      name: '西南行人-上'
    },
    {
      id: 24,
      name: '西南行人-下'
    },
    {
      id: 25,
      name: '东北行人-上'
    },
    {
      id: 26,
      name: '东北行人-下'
    },
    {
      id: 27,
      name: '西北行人-上'
    },
  {
    id: 28,
    name: '西北行人-下'
  }
];

// 行人相位图标数据（引用SVG资源）
const PED_PHASE_ICONS: PedestrianPhaseIcon[] = [
  { id: 1, name: '东行人', img: '/src/assets/sidewalk_type/east-ped.svg' },
  { id: 2, name: '西行人', img: '/src/assets/sidewalk_type/west-ped.svg' },
  { id: 3, name: '南行人', img: '/src/assets/sidewalk_type/south-ped.svg' },
  { id: 4, name: '北行人', img: '/src/assets/sidewalk_type/north-ped.svg' },
  { id: 5, name: '东行人-上', img: '/src/assets/sidewalk_type/east-top.svg' },
  { id: 6, name: '东行人-下', img: '/src/assets/sidewalk_type/east-bottom.svg' },
  { id: 7, name: '西行人-上', img: '/src/assets/sidewalk_type/west-top.svg' },
  { id: 8, name: '西行人-下', img: '/src/assets/sidewalk_type/west-bottom.svg' },
  { id: 9, name: '南行人-左', img: '/src/assets/sidewalk_type/south-left.svg' },
  { id: 10, name: '南行人-右', img: '/src/assets/sidewalk_type/south-right.svg' },
  { id: 11, name: '北行人-左', img: '/src/assets/sidewalk_type/north-left.svg' },
  { id: 12, name: '北行人-右', img: '/src/assets/sidewalk_type/north-right.svg' },
  { id: 13, name: 'X行人-/', img: '/src/assets/sidewalk_type/X-right.svg' },
  { id: 14, name: 'X行人-\\', img: '/src/assets/sidewalk_type/X-left.svg' },
  { id: 15, name: '东西路段行人', img: '/src/assets/sidewalk_type/south-north-ped.svg' },
  { id: 16, name: '南北路段行人', img: '/src/assets/sidewalk_type/east-west-ped.svg' },
  { id: 17, name: '东南行人', img: '/src/assets/sidewalk_type/southeast-crosswalk.svg' },
  { id: 18, name: '西南行人', img: '/src/assets/sidewalk_type/southwest-crosswalk.svg' },
  { id: 19, name: '东北行人', img: '/src/assets/sidewalk_type/northeast-crosswalk.svg' },
  { id: 20, name: '西北行人', img: '/src/assets/sidewalk_type/northwest-crosswalk.svg' },
  { id: 21, name: '东南行人-上', img: '/src/assets/sidewalk_type/southeast-crosswalk-up.svg' },
  { id: 22, name: '东南行人-下', img: '/src/assets/sidewalk_type/southeast-crosswalk-down.svg' },
  { id: 23, name: '西南行人-上', img: '/src/assets/sidewalk_type/southwest-crosswalk-up.svg' },
  { id: 24, name: '西南行人-下', img: '/src/assets/sidewalk_type/southwest-crosswalk-down.svg' },
  { id: 25, name: '东北行人-上', img: '/src/assets/sidewalk_type/northeast-crosswalk-up.svg' },
  { id: 26, name: '东北行人-下', img: '/src/assets/sidewalk_type/northeast-crosswalk-down.svg' },
  { id: 27, name: '西北行人-上', img: '/src/assets/sidewalk_type/northwest-crosswalk-up.svg' },
  { id: 28, name: '西北行人-下', img: '/src/assets/sidewalk_type/northwest-crosswalk-down.svg' }
];

/**
 * @Description: 根据行人相位的描述id获取对应的描述名字
 * @param list 行人相位ID数组
 * @returns 行人相位描述字符串，多个描述用逗号分隔
 */
export function getPedPhaseDesc(list: number[]): string {
  let str: string = '';
  for (const ll of list) {
    for (const image of PED_PHASE_DESCRIPTIONS) {
      if (image.id === ll) {
        str = str + ',' + image.name;
      }
    }
  }
  if (str !== '') {
    str = str.substring(1);
  }
  return str;
}

/**
 * 获取行人相位描述列表
 * @returns 行人相位描述数组
 */
export function getPedPhaseDescriptions(): PedestrianPhaseDescription[] {
  return [...PED_PHASE_DESCRIPTIONS];
}

/**
 * 根据行人相位ID获取单个相位描述
 * @param id 行人相位ID
 * @returns 行人相位描述对象或undefined
 */
export function getPedPhaseDescById(id: number): PedestrianPhaseDescription | undefined {
  return PED_PHASE_DESCRIPTIONS.find(phase => phase.id === id);
}

/**
 * 获取行人相位图标列表
 * @returns 行人相位图标数组
 */
export function getPedPhaseIcons(): PedestrianPhaseIcon[] {
  return [...PED_PHASE_ICONS];
}

/**
 * 根据行人相位ID获取图标信息
 * @param id 行人相位ID
 * @returns 行人相位图标对象或undefined
 */
export function getPedPhaseIconById(id: number): PedestrianPhaseIcon | undefined {
  return PED_PHASE_ICONS.find(icon => icon.id === id);
}

/**
 * 根据行人相位ID获取图标路径
 * @param id 行人相位ID
 * @returns 图标路径字符串
 */
export function getPedPhaseIconPath(id: number): string {
  const icon = getPedPhaseIconById(id);
  return icon?.img || '';
}

/**
 * 检查行人相位ID是否有效
 * @param id 行人相位ID
 * @returns 是否有效
 */
export function isValidPedPhaseId(id: number): boolean {
  return PED_PHASE_DESCRIPTIONS.some(phase => phase.id === id);
}

/**
 * 获取所有行人相位ID
 * @returns 行人相位ID数组
 */
export function getAllPedPhaseIds(): number[] {
  return PED_PHASE_DESCRIPTIONS.map(phase => phase.id);
}

/**
 * 根据名称搜索行人相位
 * @param name 行人相位名称（支持部分匹配）
 * @returns 匹配的行人相位描述数组
 */
export function searchPedPhasesByName(name: string): PedestrianPhaseDescription[] {
  return PED_PHASE_DESCRIPTIONS.filter(phase =>
    phase.name.toLowerCase().includes(name.toLowerCase())
  );
}

/**
 * 获取基础行人相位（ID 1-4）
 * @returns 基础行人相位描述数组
 */
export function getBasicPedPhases(): PedestrianPhaseDescription[] {
  return PED_PHASE_DESCRIPTIONS.filter(phase => phase.id >= 1 && phase.id <= 4);
}

/**
 * 获取扩展行人相位（ID > 4）
 * @returns 扩展行人相位描述数组
 */
export function getExtendedPedPhases(): PedestrianPhaseDescription[] {
  return PED_PHASE_DESCRIPTIONS.filter(phase => phase.id > 4);
}

/**
 * 根据方向获取行人相位
 * @param direction 方向关键词（如：'东'、'西'、'南'、'北'）
 * @returns 匹配方向的行人相位描述数组
 */
export function getPedPhasesByDirection(direction: string): PedestrianPhaseDescription[] {
  return PED_PHASE_DESCRIPTIONS.filter(phase =>
    phase.name.includes(direction)
  );
}
