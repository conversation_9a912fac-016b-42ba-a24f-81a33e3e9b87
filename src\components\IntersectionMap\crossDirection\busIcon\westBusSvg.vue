<template>
  <div :style="{position: 'absolute', left: Data.busleft, top: Data.bustop}" class="westbusmap">
    <div :class="Data.id >= 5 && Data.id <= 8 ? '' : 'hide'">
      <svg
        version="1.1"
        id="图层_1"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        x="0px"
        y="0px"
        viewBox="0 0 91 24"
        style="enable-background:new 0 0 91 24;"
        xml:space="preserve"
        :width="IconLengh"
        :height="IconWdith"
      >
        <g id="有轨电车-西" v-if="Data.controltype === 5">
          <path
            class="st0"
            d="M4,24h83c2.2,0,4-1.8,4-4V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4v16C0,22.2,1.8,24,4,24z"
          ></path>
          <g>
            <path
              class="st1"
              d="M46.2,13.9L46.2,13.9c0-3.4-2.8-6.2-6.2-6.2h-3.2l0.8-0.5c0.1,0,0.1-0.1,0.1-0.3c0-0.1-0.1-0.1-0.1-0.3
              l-3.1-1.2c-0.1,0-0.3,0-0.4,0.1c0,0.1,0,0.3,0.1,0.4l2.6,1l-1,0.6H14.5c-0.4,0-0.6,0.3-0.6,0.6v7.8c0,0.4,0.3,0.6,0.6,0.6h0.4h0.6
              h0.9h0.4h0.9h4.9h0.9H24h0.9h4.9h0.9h0.4H32h5.3h0.9h0.4h0.9H40h4.3C45.4,16.7,46.2,14.8,46.2,13.9z M17.2,12.5
              c0,0.3-0.3,0.5-0.5,0.5h-1.6c-0.3,0-0.5-0.3-0.5-0.5v-2.2c0-0.3,0.3-0.5,0.5-0.5h1.6c0.3,0,0.5,0.3,0.5,0.5V12.5z M20.2,12.5
              c0,0.3-0.3,0.5-0.5,0.5h-1.6c-0.3,0-0.5-0.3-0.5-0.5v-2.2c0-0.3,0.3-0.5,0.5-0.5h1.6c0.3,0,0.5,0.3,0.5,0.5V12.5z M23.5,12.6
              c0.1,0.3-0.2,0.5-0.4,0.5h-1.6c-0.3,0-0.5-0.3-0.5-0.5v-2.2c0-0.3,0.3-0.5,0.5-0.5H23c0.3,0,0.5,0.3,0.5,0.5V12.6L23.5,12.6z
               M26.5,12.5c0,0.3-0.3,0.5-0.5,0.5h-1.6c-0.3,0-0.5-0.3-0.5-0.5v-2.2c0-0.3,0.3-0.5,0.5-0.5H26c0.3,0,0.5,0.3,0.5,0.5V12.5z
               M29.6,12.5c0,0.3-0.3,0.5-0.5,0.5h-1.6c-0.3,0-0.5-0.3-0.5-0.5v-2.2c0-0.3,0.3-0.5,0.5-0.5h1.6c0.3,0,0.5,0.3,0.5,0.5V12.5z
               M32.9,12.5c0,0.3-0.3,0.5-0.5,0.5h-1.6c-0.3,0-0.5-0.3-0.5-0.5v-2.2c0-0.3,0.3-0.5,0.5-0.5h1.6c0.3,0,0.5,0.3,0.5,0.5V12.5z
               M36,12.5c0,0.3-0.3,0.5-0.5,0.5h-1.6c-0.3,0-0.5-0.3-0.5-0.5v-2.2c0-0.3,0.3-0.5,0.5-0.5h1.6c0.3,0,0.5,0.3,0.5,0.5V12.5z
               M39,12.6c0,0.3-0.3,0.5-0.5,0.5h-1.6c-0.3,0-0.5-0.3-0.5-0.5v-2.2c0-0.3,0.3-0.5,0.5-0.5h1.7c0.3,0,0.5,0.3,0.5,0.5v2.2H39z
               M43.4,14.9c0.1,0.4-0.3,0.8-0.7,0.8h-1.6c-0.4,0-0.8-0.4-0.8-0.8v-4.3c0-0.4,0.4-0.8,0.8-0.8c1.3,0,2.3,1,2.3,2.3V14.9L43.4,14.9
              z"
            ></path>
            <path
              class="st1"
              d="M13.1,17.6h34c0.3,0,0.5,0.3,0.5,0.5s-0.3,0.5-0.5,0.5h-34c-0.3,0-0.5-0.3-0.5-0.5S12.8,17.6,13.1,17.6z"
            ></path>
          </g>
        </g>
        <g id="公交车-西" v-if="Data.controltype === 3">
          <path
            class="st0"
            d="M4,24h83c2.2,0,4-1.8,4-4V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4v16C0,22.2,1.8,24,4,24z"
          ></path>
          <g>
            <path
              class="st1"
              d="M38.2,15.8c0,1,0.8,1.8,1.8,1.8s1.8-0.8,1.8-1.8S41,14,40,14C39.2,14,38.2,14.8,38.2,15.8z M38.7,15.8
              c0-0.6,0.5-1.3,1.3-1.3s1.3,0.5,1.3,1.3c0,0.6-0.5,1.3-1.3,1.3C39.2,17,38.7,16.4,38.7,15.8z"
            ></path>
            <path
              class="st1"
              d="M20.9,15.8c0,1,0.8,1.8,1.8,1.8s1.8-0.8,1.8-1.8S23.7,14,22.7,14S20.9,14.8,20.9,15.8z M21.4,15.8
              c0-0.6,0.5-1.3,1.3-1.3c0.6,0,1.3,0.5,1.3,1.3c0,0.6-0.5,1.3-1.3,1.3C22,17.1,21.4,16.4,21.4,15.8z"
            ></path>
            <path
              class="st1"
              d="M13.7,9.6v4.7c1.1,0.3,3.8,0.9,5.2,1.3h1.5c0-1.3,1.1-2.3,2.4-2.3s2.3,1,2.4,2.3h12.5c0-1.3,1.1-2.3,2.4-2.3
              s2.3,1,2.4,2.3h3.4h0.9H47c0.4,0,0.6-0.3,0.6-0.6v-3.4l-0.8-0.4v-3c0-1-0.8-1.8-1.8-1.8H17.3C15.2,6.3,13.7,7.8,13.7,9.6z M43,8.3
              c0-0.4,0.3-0.6,0.6-0.6h1c0.4,0,0.6,0.3,0.6,0.6V14c0,0.4-0.3,0.6-0.6,0.6h-0.9c-0.4,0-0.6-0.3-0.6-0.6L43,8.3L43,8.3z M36,8.1
              c0-0.3,0.3-0.5,0.5-0.5h4.9c0.3,0,0.5,0.3,0.5,0.5v2.1c0,0.3-0.3,0.5-0.5,0.5h-4.9c-0.3,0-0.5-0.3-0.5-0.5V8.1z M29.3,8.1
              c0-0.3,0.3-0.5,0.5-0.5h4.9c0.3,0,0.5,0.3,0.5,0.5v2.1c0,0.3-0.3,0.5-0.5,0.5h-4.9c-0.3,0-0.5-0.3-0.5-0.5
              C29.3,10.2,29.3,8.1,29.3,8.1z M22.5,8.1c0-0.3,0.3-0.5,0.5-0.5h5c0.3,0,0.5,0.3,0.5,0.5v2.1c0,0.3-0.3,0.5-0.5,0.5h-5
              c-0.3,0-0.5-0.3-0.5-0.5V8.1z M15.7,8.1c0-0.3,0.3-0.5,0.5-0.5h4.9c0.3,0,0.5,0.3,0.5,0.5v2.1c0,0.3-0.3,0.5-0.5,0.5h-4.9
              c-0.3,0-0.5-0.3-0.5-0.5C15.7,10.2,15.7,8.1,15.7,8.1z"
            ></path>
          </g>
        </g>
        <g id="BRT-西" v-if="Data.controltype === 4">
          <path
            class="st0"
            d="M4,24h83c2.2,0,4-1.8,4-4V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4v16C0,22.2,1.8,24,4,24z"
          ></path>
          <g>
            <path
              class="st1"
              d="M16.5,5.6h4.2c1.2,0,2,0.1,2.4,0.3C23.6,6.1,24,6.5,24.3,7c0.3,0.5,0.5,1.1,0.5,1.8c0,0.6-0.1,1.2-0.4,1.7
              s-0.6,0.9-1.1,1.1c0.6,0.2,1.1,0.6,1.5,1.1s0.5,1.2,0.5,2c0,0.9-0.2,1.7-0.7,2.4s-1,1-1.6,1.2c-0.4,0.1-1.4,0.2-3,0.2h-3.6V5.6
              H16.5z M18.6,7.7v3H20c0.8,0,1.4,0,1.6,0c0.4-0.1,0.6-0.2,0.9-0.5s0.3-0.6,0.3-1s-0.1-0.7-0.3-0.9c-0.2-0.2-0.4-0.4-0.6-0.4
              c-0.2-0.1-0.9-0.1-2-0.1h-1.3V7.7z M18.6,12.8v3.4h2c0.9,0,1.4,0,1.7-0.1c0.2-0.1,0.5-0.3,0.6-0.5c0.2-0.3,0.3-0.6,0.3-1
              s-0.1-0.8-0.3-1s-0.4-0.5-0.7-0.6c-0.3-0.1-0.9-0.2-1.8-0.2H18.6z"
            ></path>
            <path
              class="st1"
              d="M28.3,18.4V5.6h4.5c1.1,0,2,0.1,2.5,0.3s0.9,0.6,1.2,1.2S37,8.4,37,9.2c0,1-0.2,1.8-0.7,2.4
              c-0.5,0.6-1.2,1-2.1,1.2c0.5,0.3,0.8,0.7,1.1,1.1S36,15,36.5,16l1.3,2.5h-2.5l-1.5-2.8c-0.6-1-0.9-1.6-1.1-1.9s-0.4-0.4-0.6-0.5
              s-0.6-0.1-1.1-0.1h-0.4v5.4h-2.3V18.4z M30.4,11H32c1,0,1.6,0,1.8-0.1s0.5-0.3,0.6-0.5s0.2-0.6,0.2-1s-0.1-0.7-0.2-1
              S34,8,33.7,7.9c-0.2-0.1-0.8-0.1-1.7-0.1h-1.7V11H30.4z"
            ></path>
            <path class="st1" d="M42.4,18.4V7.7h-3.1V5.6h8.4v2.2h-3.1v10.7h-2.2V18.4z"></path>
          </g>
        </g>
        <g id="非机动车-西" v-if="Data.controltype === 6">
          <path
            class="st0"
            d="M4,24h83c2.2,0,4-1.8,4-4V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4v16C0,22.2,1.8,24,4,24z"
          ></path>
          <path
            class="st1"
            d="M23.9,15.1c0,2.6,2.1,4.7,4.7,4.7c2.4,0,4.4-1.8,4.7-4.1h1.4c0.1,0,0.1,0,0.1,0v-0.3l0,0l0.1,0.2
            c0,0,0,0,0.1,0c0,0,0.1,0,0.1-0.1l0,0l0,0l5.4-5.3l0.2,0.7c-1.6,0.8-2.6,2.4-2.6,4.2c0,2.6,2.1,4.7,4.7,4.7s4.7-2.1,4.7-4.7
            s-2.1-4.7-4.7-4.7c-0.2,0-0.5,0-0.7,0.1l-1.4-4.4C40.5,5.6,40,4.7,39,4.7h-1.3C37.4,4.7,37,5,37,5.5s0.3,0.7,0.7,0.7H39
            c0.1,0,0.3,0.2,0.3,0.4l0.2,0.8h-7.2L32.1,7h0.7c0.4,0,0.7-0.3,0.7-0.7c0-0.2-0.1-0.4-0.2-0.5S33,5.6,32.8,5.6h-3
            c-0.4,0-0.7,0.3-0.7,0.7c0,0.2,0.1,0.4,0.2,0.5S29.6,7,29.8,7h0.7L31,8.2l-1.2,2.5c-0.4-0.1-0.8-0.2-1.3-0.2
            C26,10.4,23.9,12.5,23.9,15.1z M39.9,8.8L35,13.7l-2-4.9C33,8.8,39.9,8.8,39.9,8.8z M29.7,14.2l0.8-1.7c0.6,0.4,1,1,1.2,1.7H29.7z
             M31.8,9.9l1.8,4.3h-0.3c-0.2-1.3-1-2.4-2-3.1L31.8,9.9z M31.8,15.7c-0.3,1.5-1.6,2.6-3.2,2.6c-1.8,0-3.2-1.5-3.2-3.2
            c0-1.8,1.5-3.2,3.2-3.2c0.2,0,0.4,0,0.6,0.1l-1.3,2.7c-0.1,0.2-0.1,0.5,0,0.7s0.4,0.3,0.6,0.3H31.8z M42.6,11.9c0.1,0,0.2,0,0.3,0
            c1.8,0,3.2,1.5,3.2,3.2c0,1.8-1.5,3.2-3.2,3.2c-1.8,0-3.2-1.5-3.2-3.2c0-1.1,0.6-2.2,1.6-2.8l1,3.1c0.1,0.4,0.5,0.6,0.9,0.5
            c0.2-0.1,0.3-0.2,0.4-0.4s0.1-0.4,0-0.6L42.6,11.9z"
          ></path>
        </g>
      </svg>
    </div>
  </div>
</template>
<script>
export default {
  name: 'westBusSvg',
  props: {
    IconLengh: {
      // 相位图标长度
      type: String,
      default: '91px'
    },
    IconWdith: {
      // 相位图标宽度
      type: String,
      default: '24px'
    },
    Data: {
      type: Object
    }
  },
  methods: {},
  mounted () {}
}
</script>
<style scoped>
.invisible {
  visibility: hidden;
}
.hide {
  display: none;
}
.st0 {
  opacity: 0.8;
  fill: #5f5f5f;
  enable-background: new;
}
.st1 {
  fill: #ffffff;
}
</style>
