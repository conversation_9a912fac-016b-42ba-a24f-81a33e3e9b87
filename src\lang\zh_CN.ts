export default {
  // 路由国际化
  route: {
    dashboard: '首页',
    document: '项目文档'
  },
  // 登录页面国际化
  login: {
    selectPlaceholder: '请选择/输入公司名称',
    username: '用户名',
    password: '密码',
    login: '登 录',
    logging: '登 录 中...',
    code: '验证码',
    rememberPassword: '记住我',
    switchRegisterPage: '立即注册',
    rule: {
      tenantId: {
        required: '请输入您的租户编号'
      },
      username: {
        required: '请输入您的账号'
      },
      password: {
        required: '请输入您的密码'
      },
      code: {
        required: '请输入验证码'
      }
    },
    social: {
      wechat: '微信登录',
      maxkey: 'MaxKey登录',
      topiam: 'TopIam登录',
      gitee: 'Gitee登录',
      github: 'Github登录'
    }
  },
  // 注册页面国际化
  register: {
    selectPlaceholder: '请选择/输入公司名称',
    username: '用户名',
    password: '密码',
    confirmPassword: '确认密码',
    register: '注 册',
    registering: '注 册 中...',
    registerSuccess: '恭喜你，您的账号 {username} 注册成功！',
    code: '验证码',
    switchLoginPage: '使用已有账户登录',
    rule: {
      tenantId: {
        required: '请输入您的租户编号'
      },
      username: {
        required: '请输入您的账号',
        length: '用户账号长度必须介于 {min} 和 {max} 之间'
      },
      password: {
        required: '请输入您的密码',
        length: '用户密码长度必须介于 {min} 和 {max} 之间',
        pattern: '不能包含非法字符：{strings}'
      },
      code: {
        required: '请输入验证码'
      },
      confirmPassword: {
        required: '请再次输入您的密码',
        equalToPassword: '两次输入的密码不一致'
      }
    }
  },
  // 导航栏国际化
  navbar: {
    full: '全屏',
    language: '语言',
    dashboard: '首页',
    document: '项目文档',
    message: '消息',
    layoutSize: '布局大小',
    selectTenant: '选择租户',
    layoutSetting: '布局设置',
    personalCenter: '个人中心',
    logout: '退出登录'
  },
  common: {
    yes: '是',
    no: '否',
    enabled: '启用',
    disabled: '禁用',
    add: '新增',
    edit: '编辑',
    delete: '删除',
    clone: '克隆',
    view: '查看',
    export: '导出',
    import: '导入',
    preview: '预览',
    download: '下载',
    upload: '上传',
    search: '搜索',
    reset: '重置',
    save: '保存',
    cancel: '取消',
    confirm: '确认',
    submit: '提交',
    close: '关闭',
    operate: '操作',
    status: '状态',
    remark: '备注',
    sort: '排序',
    action: '操作',
    select: '请选择',
    input: '请输入',
    required: '必填项',
    alarm: '提示',
    addfailed: '添加失败！',
    addsucess: '添加成功！',
    addcancel: '添加取消！',
    deletefailed: '删除失败！',
    deletesucess: '删除成功！',
    deletecancel: '删除取消！',
    updatefailed: '更新失败！',
    updatesucess: '更新成功！',
    updatecancel: '更新取消！',
    relatefailed: '关联失败！',
    relatesucess: '关联成功！',
    relatecancel: '关联取消！',
    savesucess: '保存成功！',
  },
  platform:{
    'overview': {
      'crossinfo': '路口信息',
      'crossname': '路口名称',
      'divicestate': '设备状态',
      'protocoltype': '协议类型',
      'signalID': '路口ID',
      'signalIP': '信号机IP',
      'faultinfo': '故障信息',
      'flow': '车道平均流量(h)',
      'maxFlow': '车道平均流量过大，是否继续优化？',
      'saturationflow': '车道规划饱和流量(h)',
      'typeflow-split-opt': '最小延误',
      'typecycle-opt': '均衡度最优',
      'platform': '平台类型',
      'online': '在线',
      'offline': '离线',
      'connected': '已连接',
      'onlineing': '联机中...',
      'controlmode': '控制方式',
      'manual': '手动控制',
      'exitmanual': '退出手动',
      'controlmodel': '控制模式',
      'patternname': '方案名称',
      'controlnumber': '方案编号',
      'mode': '控制方式',
      'stage': '阶段(驻留)',
      'specialcontrol': '特殊控制',
      'phaseclosecontrol': '相位关断控制',
      'phaseclose': '相位关断',
      'phaselocking': '相位锁定',
      'implement': '执行',
      'comfirm': '确定',
      'ipaddress': 'IP地址',
      'deviceport': '设备端口',
      'agreement': '协议',
      'currentstage': '当前阶段',
      'allstagesnum': '总阶段数',
      'patternnum': '方案编号',
      'details': '详情',
      'autocontrol': '多时段',
      'yellowflash': '黄闪',
      'allred': '全红',
      'step': '步进',
      'fixedperiod': '定周期',
      'inductioncontrol': '感应控制',
      'nocablecoordination': '无电缆协调',
      'phasewalk': '行人过街',
      'websteroptimization': 'Webster单点优化',
      'inductivePedestrianCrossControl': '感应式行人过街控制',
      'selfadaption': '方案生成',
      'custom': '自定义',
      'controlmodevalue': '控制方式数值',
      'patternstate': '方案状态',
      'cycle': '周期',
      'phasesplit': '绿信比',
      'phase': '相位',
      'patternoffset': '相位差',
      'coordinationtime': '协调时间',
      'getintersectionmapagain': '重新获取路口图',
      'ring': '环',
      'phaseid': '相位id',
      'detectorid': '检测器ID',
      'smallcars': '小型车数量',
      'mediumvehicles': '中型车数量',
      'bigcars': '大型车数量',
      'possessionoftime': '占有时间',
      'occupancy': '占有率',
      'phase_countdown': '相位倒计时',
      'pedcountdown': '行人倒计时',
      'split': '绿信比',
      'phasestate': '相位状态',
      'pedstate': '行人状态',
      'type': '类型',
      'showlist': 'showlist',
      'showlist1': '控制模式',
      'showlist2': '周期',
      'showlist3': '当前/剩余时间',
      'showlist4': '控制方式',
      'showlist5': '相位差',
      'showlist6': '实时流量',
      'platformcontrol': '平台控制',
      'configurationtoolcontrol': '配置工具控制',
      'manualcontrol': '手动面板控制',
      'tips': '提示',
      'exitmanul': '退出前需要先恢复自主控制, 是否退出?',
      'canceled': '已取消',
      'nextcycleeffic': '执行成功，下周期生效！',
      'transitioneffic': '执行成功，过渡切换后生效！',
      'controlnumerrormess': '控制方式为自定义时，控制方式编号不能为空！',
      'delay': '延迟时间',
      'duration': '持续时间',
      'curModel': '控制方式',
      'curStage': '当前阶段',
      'responseTime': '请求耗时',
      'nofault': '无',
      'extendedContent': '扩展内容',
      'JSONFormatError': 'JSON格式错误!',
      'showFault': '显示故障详情',
      'hideFault': '隐藏故障详情',
      'close': '关断',
      'closemode': '方式',
      'closeOption0': '恢复',
      'closeOption1': '关断',
      'closeOption2': '仅机动车关断',
      'closeOption3': '仅行人关断',
      'lockingOption0': '恢复',
      'lockingOption1': '锁定',
      'lockingOption2': '仅机动车锁定',
      'lockingOption3': '仅行人锁定',
      'vehicle': '机动车',
      'pedestrian': '行人',
      'confirmed': '已确认',
      'ignored': '已忽略',
      'untreated': '未处理',
      'item': '条',
      'configInfo': '*点击路口渠化图中的方向箭头锁定特勤方向',
      'more': '更多',
      'configEffectiveDir': '配置有效方向',
      'parallelLineReminder': '并线提醒',
      'effectiveDir': '有效方向'
    },
    'control': {
      'mode': '模式:',
      'control': '控制:',
      'cycle': '周期:',
      'offset': '相位差:',
      'curTime': '当前时间:',
      'syncTime': '剩余时间:',
      'score': '分数:',
      'level': '等级:',
      'patternid': '方案编号:',
      'name': '名称:',
      'current_phase': '当前相位:',
      'phase': '相位',
      'ring': '环',
      'control_style': '控制方式',
      'mode_style': '控制模式',
      'pattern': '控制编号',
      'phase_countdown': '相位倒计时:',
      'split': '绿信比:',
      'countdown': '倒计时:',
      'id': '相位id:',
      'type': '相位类型:',
      'light_countdown': '通道倒计时:',
      'sequence': '相序:',
      'num': '序号：',
      'value': '控制参数',
      'currentvolume': '实时流量',
      'time': '时间',
      'overlap': '跟随相位',
      'stagecountdown': '阶段倒计时',
      'stageduration': '阶段时长'
    },
    'button': {
      'OK': '确定',
      'Cancel': '取消',
      'Back': '返回',
      'ignore': '忽略',
      'confirm': '确认',
      'save': '保存',
      'reset': '重置',
      'update': '更新'
    },
    'common': {
      'patterntips': '方案图支持拖拽和编辑,点击数字修改绿信比,相序只支持同一个Barrier中拖拽修改',
      'barrier': '生成屏障',
      'stagechangering': '阶段转环配置',
      'allSelected': '全选',
      'week0': '星期日',
      'week1': '星期一',
      'week2': '星期二',
      'week3': '星期三',
      'week4': '星期四',
      'week5': '星期五',
      'week6': '星期六',
      'confirm': '确认',
      'isChange': '环转阶段',
      'changestage': '阶段方案的数据将被重置，是否确定?',
      'isdown': '下载参数前请先上载同步最新参数，是否下载',
      'addnumber': '添加编号',
      'ringStyle': '环视图',
      'changecycle': '周期等比例切换',
      'isringstage': '是否转换阶段模式',
      'stageStyle': '阶段视图',
      'query': '查询',
      'cancel': '取消',
      'add': '添加',
      'save': '参数保存成功!',
      'delete': '删除',
      'clone': '克隆',
      'deleteall': '全部删除',
      'update': '修改',
      'search': '搜索',
      'alarm': '提示',
      'info': '提示',
      'none': '无',
      'weak': '弱',
      'normal': '普通',
      'strong': '强',
      'name': '名 称',
      'path': '路 径',
      'description': '描 述',
      'operations': '操 作',
      'addfailed': '添加失败！',
      'addsucess': '添加成功！',
      'addcancel': '添加取消！',
      'deletefailed': '删除失败！',
      'deletesucess': '删除成功！',
      'deletecancel': '删除取消！',
      'updatefailed': '更新失败！',
      'updatesucess': '更新成功！',
      'updatecancel': '更新取消！',
      'relatefailed': '关联失败！',
      'relatesucess': '关联成功！',
      'relatecancel': '关联取消！',
      'savesucess': '保存成功！',
      'upload': '上传',
      'select': '请选择',
      'input': '请输入',
      'entercontent': '请输入内容',
      'enter': '请输入',
      'commit': '提交',
      'commitandexecute': '提交并执行',
      'uploadsuccess': '参数上载成功!',
      'restoreDefaultSuccess': '参数恢复成功!',
      'restoreDefaultFailed': '参数恢复失败!',
      'download': '参数下载成功!',
      'downloaderror': '信号机校验失败！',
      'downloadandrun': '提交并运行成功！',
      'editagentid': '编辑设备ID',
      'repeatid': 'ID重复',
      'querysucess': '查询成功！',
      'setup': '设置',
      'tipsmodaltitle': '提示',
      'deviceoffline': '设备不在线！',
      'operationsuccess': '操作成功',
      'tips': '固定月日的优先级高于星期，当日期优先级相同时，越靠前的日期执行优先级越高',
      'savesuccess': '保存成功!',
      'savefailed': '保存失败!'
    },
    'message': {
      // 全局异常错误
      '0001': '空指针',
      '0002': '错误请求方式',
      '0003': '输入参数类型不正确',
      '0004': '输入参数不全',
      '0005': '输入参数不满足约束',
      '0006': '系统错误',
      '0007': '名称为空',
      '0008': '服务过期 !',
      'errorcode': '错误码',
      '10000': '参数为空',
      '10001': '参数长度错误',
      '1002': '错误',
      '10002': '记录不能为空',
      '20003': 'id不能为空且不能为0',
      '20004': '无参数记录',
      '20005': '没有发现设备id',
      '20006': '数据流关闭失败!',
      '20007': 'Json类型转换失败!',
      '20008': '文件读取失败!',
      '20009': '文件不存在!',
      '20010': 'I/O关闭错误!',
      // 用户管理错误
      '3001': '用户名不存在',
      '3002': '认证失败',
      '3003': '用户名重复',
      '3004': '新旧密码不能相同',
      '3005': '密码不能为空',
      '3006': '原密码错误',
      '3007': '登录用户不存在',
      '3008': '没有权限访问',
      '3009': '密码错误',
      '3010': '超级用户角色不能被修改',
      '3011': '用户名密码错误',
      '3013': '账号已停用',
      '3014': 'token已过期',
      '3015': 'token失效',
      '3016': '用户组织机构为空',
      '3017': '组织机构不存在',
      '3018': '访问ip与用户ip不一致，禁止登录',
      '3019': '角色无法删除，还有关联的用户',
      '3020': '添加角色失败，角色名称重复!',
      '3021': '无法添加超级管理员角色或用户！',
      '3022': '无法移除管理员角色！',
      '3023': '管理员权限不能被修改！',
      '3024': '当前用户没有被创建！',
      '3025': 'token未启用！',
      '3026': '角色存在关联的用户!',
      '3027': '角色不存在!',
      // 消息通讯错误
      '4001': '错误请求',
      '4002': '错误应答',
      '4003': '设备不在线',
      '4004': '重复的设备id',
      '4005': '信号机应答为空',
      '4006': '信号机应答操作类型为空',
      '4007': '控制消息应答错误',
      '4008': '信号机不在线，请求已忽略',
      '4009': '参数缓存为空，请先上载参数',
      '4010': '请求数据失败',
      '4011': '设备不支持的消息类型',
      // 错误子类型
      // error-request
      '4100': 'agentid为空',
      '4101': '操作类型为空!',
      '4102': '消息类型为空!',
      '4103': '消息数据为空!',
      '4104': 'JSON格式错误!',
      '4105': 'agentid不存在!',
      '4106': '未知的操作类型!',
      '4107': '未知的消息类型!',
      '4108': '协议为空!',
      '4109': '接收数据超时或收到错误数据!',
      '4110': '上一次发送的数据还未完成!',
      '4111': '端口为空!',
      '4112': '缓存为空!',
      '4113': '车队尾红波协调时关键路口不能为空',
      '4114': '车队尾红波协调时关键路口不能为前两个（上行）',
      '4115': '车队尾红波协调时关键路口不能为后两个（下行）',
      '4116': '车队尾红波协调时参与协调的路口至少为3个',
      // error-response
      '4200': '应答为空!',
      '4201': '应答消息转换错误!',
      '4203': '未知的请求指示!',
      '4204': 'Comm数据发送错误!',
      '4205': 'Udp接收消息类型错误',
      '4206': '从接收信息中没找到UdpCommunication',
      '4207': '下载异常导致应答错误',
      '4208': '应答的消息协议不兼容',
      '4209': '信号机应答错误',
      // device not online
      '4301': '设备不在线',
      // 协调路线错误
      '5001': '重复的协调路线名称',
      // 勤务路线错误
      '6001': '重复的勤务路线名称',
      '6002': '勤务路线正在执行中',
      '6003': '特勤路线无法取消! 方案已经改变.',
      // 流量错误
      '7001': 'ftp客户端连接失败',
      // 组织机构错误
      '11001': '名称重复!',
      // 路口管理错误
      '8001': '通过路口ID未找到路口!',
      '8002': '路口位于协调路线上',
      '8003': '路口位于特勤路线上',
      '8004': '路口ID重复',
      // 错误类型子类型，返回码第二层
      '8101': '路口不允许删除!',
      // 瓶颈控制错误
      '9001': '瓶颈控制执行失败!',
      '9002': '瓶颈控制恢复失败!',
      '9003': '正在执行瓶颈控制，无法删除!',
      '9004': '瓶颈控制方案名称已存在!',
      '9005': '瓶颈控制方案为空！',
      '9006': '区域控制未执行!'
    }
  }
};
