import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  SignalManagementVO,
  SignalManagementForm,
  SignalManagementQuery,
  MessageData
} from '@/api/business/signalManagement/types';

/**
 * 查询信号机管理列表
 * @param query
 * @returns {*}
 */

export const listSignalManagement = (query?: SignalManagementQuery): AxiosPromise<SignalManagementVO[]> => {
  return request({
    url: '/business/signalManagement/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询信号机管理详细
 * @param id
 */
export const getSignalManagement = (id: string | number): AxiosPromise<SignalManagementVO> => {
  return request({
    url: '/business/signalManagement/' + id,
    method: 'get'
  });
};

/**
 * 新增信号机管理
 * @param data
 */
export const addSignalManagement = (data: SignalManagementForm) => {
  return request({
    url: '/business/signalManagement',
    method: 'post',
    data: data
  });
};

/**
 * 修改信号机管理
 * @param data
 */
export const updateSignalManagement = (data: SignalManagementForm) => {
  return request({
    url: '/business/signalManagement',
    method: 'put',
    data: data
  });
};

/**
 * 删除信号机管理
 * @param id
 */
export const delSignalManagement = (id: string | number | Array<string | number>) => {
  return request({
    url: '/business/signalManagement/' + id,
    method: 'delete'
  });
};

/**
 * 查询信号机下拉框数据
 */
export const listSignalDrop = (): AxiosPromise<SignalManagementVO[]> => {
  return request({
    url: '/business/signalManagement/optionselect',
    method: 'get'
  });
};

/**
 * 发送设备消息
 * @param data
 */
export const postDevsMessage = (data: MessageData) => {
  return request({
    url: '/business/signalManagement/postDevsMessage',
    method: 'post',
    data: data
  });
};

export const validdirections = (id: string | number) => {
  return request({
    url: '/business/signalManagement/validdirections/' + id,
    method: 'get'
  });
};

export const mergedirections = (id: string | number) => {
  return request({
    url: '/business/signalManagement/mergedirections/' + id,
    method: 'get'
  });
};
