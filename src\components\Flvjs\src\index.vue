<template>
  <div class="flvlist">
    <FlvPlayer
      v-if="!resetVedioDom"
      :Width="Width"
      :Height="Height"
      :Title="Title"
      :curVideoInfo="curVideoInfo"
      :autoPlay="autoPlay"
      @resetComponent="reset"
    >
      <!-- 多视频切换控制 -->
      <template #vediolist>
        <el-dropdown trigger="click" @command="switchVideo">
          <span class="el-dropdown-link">
            <i class="iconfont icon-shipincaidan"></i>
          </span>
          <template #dropdown>
            <el-dropdown-menu class="flvlist-dropdown">
              <el-dropdown-item
                v-for="video in curDevVideos"
                :command="video.id"
                :key="video.id"
                :class="video.id === curVideoInfo?.id ? 'active' : ''"
              >
                <i :class="video.id === curVideoInfo?.id ? 'iconfont icon-fangzhenzanting' : 'iconfont icon-fangzhenkaishi'"></i>
                <span>{{ video.name }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </FlvPlayer>
    <div v-if="!curVideoInfo" class="noVideo">当前路口未接入视频/当前路口视频异常</div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick, defineComponent } from 'vue'
import FlvPlayer from './singlevideo.vue'
import type { VideoInfo, FlvProps } from './types/index'

// 定义props
interface Props extends FlvProps {}

const props = withDefaults(defineProps<Props>(), {
  curDevVideos: () => [],
  Width: '100%',
  Height: '237',
  autoPlay: false
})

// 响应式数据
const resetVedioDom = ref<boolean>(false)
const curVideoInfo = ref<VideoInfo | null>(null)

// 监听curDevVideos变化
watch(
  () => props.curDevVideos,
  (val: VideoInfo[]) => {
    if (!val.length) return
    curVideoInfo.value = props.curDevVideos[0]
    switchVideo(val[0].id)
  },
  { deep: true }
)

// 组件挂载后初始化
onMounted(() => {
  if (props.curDevVideos.length > 0) {
    curVideoInfo.value = props.curDevVideos[0]
  }
})

// 重置组件
const reset = async (nextmediaid: string | number) => {
  resetVedioDom.value = true
  await nextTick()
  resetVedioDom.value = false
  const targetVideo = props.curDevVideos.find(ele => ele.id === nextmediaid)
  if (targetVideo) {
    curVideoInfo.value = targetVideo
  }
}

// 切换视频
const switchVideo = (nextid: string | number) => {
  if (nextid === curVideoInfo.value?.id) return
  reset(nextid)
}
</script>

<style lang="scss" scoped>
@import './styles/index.scss';
</style>
