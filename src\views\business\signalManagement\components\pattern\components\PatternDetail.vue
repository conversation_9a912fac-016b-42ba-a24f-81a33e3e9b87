<template>
  <el-tabs v-model="activeTab" type="card">
    <!-- 环配置 -->
    <el-tab-pane v-if="!isStageMode" name="ring" label="环配置">
      <div class="ring-container">
        <RingKanban
          v-for="n in ringCount"
          :key="n"
          :ring-number="n"
          :phases="pattern.rings[n - 1] || []"
          @phase-change="handlePhaseChange"
        />
      </div>
    </el-tab-pane>
    
    <!-- 阶段配置 -->
    <el-tab-pane v-if="isStageMode" name="stage" label="阶段配置">
      <div class="stage-container">
        <StageKanban
          v-for="(stage, index) in pattern.stagesList"
          :key="index"
          :stage="stage"
          :stage-index="index"
          @stage-change="handleStageChange"
        />
        <el-button
          type="primary"
          icon="Plus"
          circle
          @click="handleAddStage"
          class="add-stage-btn"
        />
      </div>
    </el-tab-pane>
    
    <!-- 参数配置 -->
    <el-tab-pane v-if="!isStageMode" name="params" label="参数配置">
      <div class="params-container">
        <PatternParams :pattern="pattern" @params-change="handleParamsChange" />
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { ref } from 'vue'
// import RingKanban from './RingKanban.vue'
// import StageKanban from './StageKanban.vue'
// import PatternParams from './PatternParams.vue'
import type { Pattern, Stage, Phase } from '../types'

interface Props {
  pattern: Pattern
  index: number
  isStageMode: boolean
  ringCount: number
  phaseList: Phase[]
}

interface Emits {
  (e: 'add-stage'): void
  (e: 'stage-change', stageIndex: number, stage: Stage): void
  (e: 'phase-change', ringIndex: number, phases: Phase[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const activeTab = ref(props.isStageMode ? 'stage' : 'ring')

const handleAddStage = () => {
  emit('add-stage')
}

const handleStageChange = (stageIndex: number, stage: Stage) => {
  emit('stage-change', stageIndex, stage)
}

const handlePhaseChange = (ringIndex: number, phases: Phase[]) => {
  emit('phase-change', ringIndex, phases)
}

const handleParamsChange = (params: any) => {
  // 处理参数变更
}
</script>

<style scoped>
.ring-container,
.stage-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  overflow-x: auto;
  padding: 20px 0;
}

.add-stage-btn {
  margin-left: 20px;
}

.params-container {
  padding: 20px;
}
</style>

