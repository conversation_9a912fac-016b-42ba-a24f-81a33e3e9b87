// 路口图组件类型定义

export interface PhaseCountdownItem {
  id: number;
  phaseCountdown: number;
  phaseCountdownColor: string;
  showlist: ShowListItem[];
}

export interface ShowListItem {
  id: string | number;
  peddirection?: PedDirectionItem[];
  color: string;
}

export interface PedDirectionItem {
  id: number;
  name: string;
  color: string;
}

export interface PhaseStatusItem {
  id: number;
  type: number;
  countdown?: number;
  pedtype?: number;
}

export interface OverlapStatusItem {
  id: number;
  type: number;
  countdown?: number;
  pedtype?: number;
  mphase: number[];
}

export interface LanePhaseDataItem {
  key: string;
  phaseid: number;
  id: number | string;
  name: string;
  left: number;
  top: number;
  type?: number | string;
  color?: string;
  phaseCountdown?: number;
  flag?: string;
  controltype?: number;
}

export interface SidewalkPhaseDataItem {
  key: string;
  phaseid: number;
  id: number | string;
  name: string;
  left: number;
  top: number;
  pedtype?: number | string;
  color?: string;
  flag?: string;
}

export interface BusPhaseDataItem {
  key: string;
  phaseid: number;
  id: number | string;
  name: string;
  left: number;
  top: number;
  busleft: number;
  bustop: number;
  controltype: number;
  type?: number | string;
  color?: string;
  phaseCountdown?: number;
  flag?: string;
}

export interface ChannelStatusItem {
  id: number;
  type: number;
  realdir?: number[];
  status?: number;
}

export interface CrossStatusData {
  control: number;
  phase?: PhaseStatusItem[];
  overlap?: OverlapStatusItem[];
  current_phase?: number[];
  current_stage?: number;
  current_stagecd?: number;
  channellamp?: ChannelStatusItem[];
  stages?: number[][];
}

export interface CrossInfo {
  phaseList: PhaseListItem[];
  overlaplList?: OverlapListItem[];
  param?: {
    patternList: PatternListItem[];
  };
}

export interface PhaseListItem {
  id: number;
  direction: number[];
  peddirection?: number[];
  controltype?: number;
}

export interface OverlapListItem {
  id: number;
  direction?: number[];
  peddirection?: number[];
  mphase: number[];
}

export interface PatternListItem {
  rings?: RingItem[];
  contrloType?: string;
}

export interface RingItem {
  num: number;
  phases: number[];
}

export interface EffectDirDataItem {
  key: string;
  id: number;
  name: string;
  left: number;
  top: number;
  color: string;
}

export interface XdrDataItem {
  left: string;
  top: string;
}

export type RoadDirection = 'right' | 'left';
export type CrossType = 'Crossroads' | 'TypeT-east' | 'TypeT-west' | 'TypeT-north' | 'TypeT-south' | 'Customroads' | 'ramp-east' | 'ramp-west' | 'ramp-north' | 'ramp-south' | 'ped-section-east-west' | 'ped-section-south-north';
export type MainType = '100' | '101' | '103' | '104' | '999';
export type ControlType = 'ring' | 'stage';
