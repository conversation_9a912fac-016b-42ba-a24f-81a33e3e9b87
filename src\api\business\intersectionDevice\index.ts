import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { IntersectionDeviceVO, IntersectionDeviceForm, IntersectionDeviceQuery } from '@/api/business/intersectionDevice/types';

/**
 * 查询路口设备列表
 * @param query
 * @returns {*}
 */

export const listIntersectionDevice = (query?: IntersectionDeviceQuery): AxiosPromise<IntersectionDeviceVO[]> => {
  return request({
    url: '/business/intersectionDevice/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询路口设备详细
 * @param id
 */
export const getIntersectionDevice = (id: string | number): AxiosPromise<IntersectionDeviceVO> => {
  return request({
    url: '/business/intersectionDevice/' + id,
    method: 'get'
  });
};

/**
 * 新增路口设备
 * @param data
 */
export const addIntersectionDevice = (data: IntersectionDeviceForm) => {
  return request({
    url: '/business/intersectionDevice',
    method: 'post',
    data: data
  });
};

/**
 * 修改路口设备
 * @param data
 */
export const updateIntersectionDevice = (data: IntersectionDeviceForm) => {
  return request({
    url: '/business/intersectionDevice',
    method: 'put',
    data: data
  });
};

/**
 * 删除路口设备
 * @param id
 */
export const delIntersectionDevice = (id: string | number | Array<string | number>) => {
  return request({
    url: '/business/intersectionDevice/' + id,
    method: 'delete'
  });
};

export const getIntersectionDeviceVideoList = (id: string | number): AxiosPromise<IntersectionDeviceVO> => {
  return request({
    url: '/business/intersectionDevice/videoList/' + id,
    method: 'get'
  });
};
