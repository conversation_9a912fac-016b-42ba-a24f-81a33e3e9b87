<template>
  <div>
    <!-- 公交图标底图 -->
      <!-- 东 -->
      <EastBusIcon :Data="Data"/>
      <!-- 西 -->
      <WestBusSvg :Data="Data"/>
      <!-- 南 -->
      <SouthBusSvg :Data="Data"/>
      <!-- 北 -->
      <NorthBusSvg :Data="Data"/>
  </div>
</template>
<script>
import EastBusIcon from './eastBusSvg'
import WestBusSvg from './westBusSvg'
import NorthBusSvg from './northBusSvg'
import SouthBusSvg from './southBusSvg'
export default {
  name: 'busmapsvg',
  data () {
    return {
    }
  },
  components: {
    EastBusIcon,
    WestBusSvg,
    NorthBusSvg,
    SouthBusSvg
  },
  props: {
    Data: {
      type: Object
    }
  },
  methods: {},
  mounted () {}
}
</script>
