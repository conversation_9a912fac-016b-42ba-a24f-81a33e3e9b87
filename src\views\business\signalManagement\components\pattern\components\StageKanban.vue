<template>
  <div class="stage-kanban">
    <div class="stage-header">
      <h4>阶段 {{ stage.stageNo }}</h4>
    </div>
    
    <div class="stage-content">
      <el-form :model="localStage" label-width="80px" size="small">
        <el-form-item label="绿灯时间">
          <el-input-number
            v-model="localStage.green"
            :min="1"
            :max="300"
            @change="handleChange"
          />
        </el-form-item>
        
        <el-form-item label="黄灯时间">
          <el-input-number
            v-model="localStage.yellow"
            :min="1"
            :max="10"
            @change="handleChange"
          />
        </el-form-item>
        
        <el-form-item label="红灯时间">
          <el-input-number
            v-model="localStage.red"
            :min="1"
            :max="10"
            @change="handleChange"
          />
        </el-form-item>
        
        <el-form-item label="最小绿灯">
          <el-input-number
            v-model="localStage.min"
            :min="1"
            :max="300"
            @change="handleChange"
          />
        </el-form-item>
        
        <el-form-item label="最大绿灯">
          <el-input-number
            v-model="localStage.max"
            :min="1"
            :max="300"
            @change="handleChange"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { Stage } from '../types'

interface Props {
  stage: Stage
  stageIndex: number
}

interface Emits {
  (e: 'stage-change', stageIndex: number, stage: Stage): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const localStage = ref<Stage>({ ...props.stage })

const handleChange = () => {
  emit('stage-change', props.stageIndex, localStage.value)
}

watch(() => props.stage, (newStage) => {
  localStage.value = { ...newStage }
}, { deep: true })
</script>

<style scoped>
.stage-kanban {
  min-width: 250px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
}

.stage-header {
  background: #4a9ff9;
  color: white;
  padding: 10px;
  text-align: center;
}

.stage-content {
  padding: 15px;
}
</style>

