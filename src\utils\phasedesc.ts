// 相位描述接口
export interface PhaseDescription {
  id: number;
  name: string;
}

// 相位图标接口
export interface PhaseIcon {
  id: number;
  name: string;
  class: string;
  leftclass?: string;
}

// 道路方向类型
export type RoadDirection = 'left' | 'right';

// 相位描述数据
const PHASE_DESCRIPTIONS: PhaseDescription[] = [
    {
      id: 1,
      name: '东直行'
    },
    {
      id: 2,
      name: '东左转'
    },
    {
      id: 3,
      name: '东右转'
    },
    {
      id: 4,
      name: '东掉头'
    },
    {
      id: 5,
      name: '西直行'
    },
    {
      id: 6,
      name: '西左转'
    },
    {
      id: 7,
      name: '西右转'
    },
    {
      id: 8,
      name: '西掉头'
    },
    {
      id: 9,
      name: '北直行'
    },
    {
      id: 10,
      name: '北左转'
    },
    {
      id: 11,
      name: '北右转'
    },
    {
      id: 12,
      name: '北掉头'
    },
    {
      id: 13,
      name: '南直行'
    },
    {
      id: 14,
      name: '南左转'
    },
    {
      id: 15,
      name: '南右转'
    },
    {
      id: 16,
      name: '南掉头'
    },
    {
      id: 17,
      name: '东南直行'
    },
    {
      id: 18,
      name: '东南左转'
    },
    {
      id: 19,
      name: '东南右转'
    },
    {
      id: 20,
      name: '东南掉头'
    },
    {
      id: 21,
      name: '西南直行'
    },
    {
      id: 22,
      name: '西南左转'
    },
    {
      id: 23,
      name: '西南右转'
    },
    {
      id: 24,
      name: '西南掉头'
    },
    {
      id: 25,
      name: '东北直行'
    },
    {
      id: 26,
      name: '东北左转'
    },
    {
      id: 27,
      name: '东北右转'
    },
    {
      id: 28,
      name: '东北掉头'
    },
    {
      id: 29,
      name: '西北直行'
    },
    {
      id: 30,
      name: '西北左转'
    },
    {
      id: 31,
      name: '西北右转'
    },
  {
    id: 32,
    name: '西北掉头'
  }
];

// 相位图标数据
const PHASE_ICONS: PhaseIcon[] = [
  { id: 1, name: '东直行', class: 'iconfont icon-dongzhihang' },
  { id: 2, name: '东左转', class: 'iconfont icon-dongzuozhuan' },
  { id: 3, name: '东右转', class: 'iconfont icon-dongyouzhuan' },
  { id: 4, name: '东掉头', class: 'iconfont icon-dongdiaotou', leftclass: 'iconfont icon-dongtiaotou-yinni' },
  { id: 5, name: '西直行', class: 'iconfont icon-xizhihang' },
  { id: 6, name: '西左转', class: 'iconfont icon-xizuozhuan' },
  { id: 7, name: '西右转', class: 'iconfont icon-xiyouzhuan' },
  { id: 8, name: '西掉头', class: 'iconfont icon-xidiaotou', leftclass: 'iconfont icon-xitiaotou-yinni' },
  { id: 9, name: '北直行', class: 'iconfont icon-beizhihang' },
  { id: 10, name: '北左转', class: 'iconfont icon-beizuozhuan' },
  { id: 11, name: '北右转', class: 'iconfont icon-beiyouzhuan' },
  { id: 12, name: '北掉头', class: 'iconfont icon-beidiaotou', leftclass: 'iconfont icon-beitiaotou-yinni' },
  { id: 13, name: '南直行', class: 'iconfont icon-nanzhihang' },
  { id: 14, name: '南左转', class: 'iconfont icon-nanzuozhuan' },
  { id: 15, name: '南右转', class: 'iconfont icon-nanyouzhuan' },
  { id: 16, name: '南掉头', class: 'iconfont icon-nandiaotou', leftclass: 'iconfont icon-nantiaotou-yinni' },
  { id: 17, name: '东南直行', class: 'iconfont icon-dongnanzhihang' },
  { id: 18, name: '东南左转', class: 'iconfont icon-dongnanzuozhuan' },
  { id: 19, name: '东南右转', class: 'iconfont icon-dongnanyouzhuan' },
  { id: 20, name: '东南掉头', class: 'iconfont icon-dongnandiaotou' },
  { id: 21, name: '西南直行', class: 'iconfont icon-xinanzhihang' },
  { id: 22, name: '西南左转', class: 'iconfont icon-xinanzuozhuan' },
  { id: 23, name: '西南右转', class: 'iconfont icon-xinanyouzhuan' },
  { id: 24, name: '西南掉头', class: 'iconfont icon-xinandiaotou' },
  { id: 25, name: '东北直行', class: 'iconfont icon-dongbeizhihang' },
  { id: 26, name: '东北左转', class: 'iconfont icon-dongbeizuozhuan' },
  { id: 27, name: '东北右转', class: 'iconfont icon-dongbeiyouzhuan' },
  { id: 28, name: '东北掉头', class: 'iconfont icon-dongbeidiaotou' },
  { id: 29, name: '西北直行', class: 'iconfont icon-xibeizhihang' },
  { id: 30, name: '西北左转', class: 'iconfont icon-xibeizuozhuan' },
  { id: 31, name: '西北右转', class: 'iconfont icon-xibeiyouzhuan' },
  { id: 32, name: '西北掉头', class: 'iconfont icon-xibeidiaotou' }
];

/**
 * @Description: 根据相位的描述id获取对应的描述名字
 * @param list 相位ID数组
 * @returns 相位描述字符串，多个描述用逗号分隔
 */
export function getPhaseDesc(list: number[]): string {
  let str: string = '';
  for (const ll of list) {
    for (const image of PHASE_DESCRIPTIONS) {
      if (image.id === ll) {
        str = str + ',' + image.name;
      }
    }
  }
  if (str !== '') {
    str = str.substring(1);
  }
  return str;
}

/**
 * 获取相位描述列表
 * @returns 相位描述数组
 */
export function getPhaseDescriptions(): PhaseDescription[] {
  return [...PHASE_DESCRIPTIONS];
}

/**
 * 根据相位ID获取单个相位描述
 * @param id 相位ID
 * @returns 相位描述对象或undefined
 */
export function getPhaseDescById(id: number): PhaseDescription | undefined {
  return PHASE_DESCRIPTIONS.find(phase => phase.id === id);
}

/**
 * 获取相位图标列表（支持道路方向）
 * @param roadDirection 道路方向，默认为'right'
 * @returns 相位图标数组
 */
export function getPhaseIcons(roadDirection: RoadDirection = 'right'): PhaseIcon[] {
  const icons = [...PHASE_ICONS];

  if (roadDirection === 'left') {
    // 左行下，掉头图标替换
    icons.forEach((icon) => {
      if ([4, 8, 12, 16].includes(icon.id) && icon.leftclass) {
        icon.class = icon.leftclass;
      }
    });
  }

  return icons;
}

/**
 * 根据相位ID获取图标信息
 * @param id 相位ID
 * @param roadDirection 道路方向，默认为'right'
 * @returns 相位图标对象或undefined
 */
export function getPhaseIconById(id: number, roadDirection: RoadDirection = 'right'): PhaseIcon | undefined {
  const icons = getPhaseIcons(roadDirection);
  return icons.find(icon => icon.id === id);
}

/**
 * 根据相位ID获取图标CSS类名
 * @param id 相位ID
 * @param roadDirection 道路方向，默认为'right'
 * @returns CSS类名字符串
 */
export function getPhaseIconClass(id: number, roadDirection: RoadDirection = 'right'): string {
  const icon = getPhaseIconById(id, roadDirection);
  return icon?.class || `phase-icon-${id}`;
}

/**
 * 检查相位ID是否有效
 * @param id 相位ID
 * @returns 是否有效
 */
export function isValidPhaseId(id: number): boolean {
  return PHASE_DESCRIPTIONS.some(phase => phase.id === id);
}

/**
 * 获取所有相位ID
 * @returns 相位ID数组
 */
export function getAllPhaseIds(): number[] {
  return PHASE_DESCRIPTIONS.map(phase => phase.id);
}

/**
 * 根据名称搜索相位
 * @param name 相位名称（支持部分匹配）
 * @returns 匹配的相位描述数组
 */
export function searchPhasesByName(name: string): PhaseDescription[] {
  return PHASE_DESCRIPTIONS.filter(phase =>
    phase.name.toLowerCase().includes(name.toLowerCase())
  );
}
