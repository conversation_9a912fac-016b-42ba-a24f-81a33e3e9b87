<template>
<div class="intersection-map">
  <div class="crossDirection-display openatc-intersection-base-map" :class="{
    'widescreenCrossImg': bodyDomWidth > 1680,
    'superlargeCrossImg': bodyDomWidth <= 1680 && bodyDomWidth > 1440,
    'largeCrossImg': bodyDomWidth <= 1440 && bodyDomWidth > 1280,
    'middleCrossImg2': bodyDomWidth <= 1280 && bodyDomWidth > 960,
    'smallCrossImg': bodyDomWidth <= 960 && bodyDomWidth > 890,
    'smallCrossImg2': bodyDomWidth <= 890 && bodyDomWidth > 720,
    'miniCrossImg': bodyDomWidth <= 720 && bodyDomWidth > 650,
    'superminiCrossImg': bodyDomWidth <= 650 && bodyDomWidth > 450,
    'transMiddleCrossImg': bodyDomWidth <= 450 && bodyDomWidth > 350,
    'transMiddleCrossImg2': bodyDomWidth <= 350 && bodyDomWidth > 300,
    'transMiddleCrossImg3': bodyDomWidth <= 300 && bodyDomWidth > 260,
    'transMiniCrossImg': bodyDomWidth <= 260,
    'changePaddingBottom': graphicMode }">
    <CrossDiagram ref = "crossDiagram" v-if="reset"
      :crossStatusData="crossStatusData"
      :devId="devId"
      :isShowInterval="isShowInterval"
      :isShowState="isShowState"
      :isShowMode="isShowMode"
      :modeName="modeName"
      :controlName="controlName"
      :stateName="stateName"
      :roadDirection="roadDirection"
      :choosedDirection="choosedDirection"
      :choosedPedDirection="choosedPedDirection"
      :isVipRoute="isVipRoute"
      :clickMode="clickMode"
      :isThirdSignal="isThirdSignal"
      :channelType="channelType"
      :isShowMessage ="isShowMessage"
      @handleClickCrossIcon="handleClickCrossIcon" />
  </div>
</div>
</template>

<script lang="ts">
import { defineComponent, nextTick, type PropType } from 'vue'
import { useRoute } from 'vue-router'
import CrossDiagram from './crossDirection/crossDiagram.vue'

// 接口定义
interface BodyDomSize {
  width: number
  height: number
}

interface CrossStatusData {
  [key: string]: any
}

export default defineComponent({
  name: 'intersection-base-map',
  components: {
    CrossDiagram
  },
  props: {
    crossStatusData: {
      type: Object as PropType<CrossStatusData>,
      required: true
    },
    devId: {
      type: Number,
      default: 0
    },
    graphicMode: {
      type: Boolean,
      default: false
    },
    isShowInterval: {
      type: Boolean,
      default: true
    },
    isShowMessage: {
      type: Boolean,
      default: true
    },
    roadDirection: {
      type: String,
      default: 'right'
    },
    isShowState: {
      type: Boolean,
      default: false
    },
    isShowMode: {
      type: Boolean,
      default: false
    },
    modeName: {
      type: String,
      default: ''
    },
    controlName: {
      type: String,
      default: ''
    },
    stateName: {
      type: String,
      default: ''
    },
    choosedDirection: {
      type: Array as PropType<any[]>,
      default: () => []
    },
    choosedPedDirection: {
      type: Array as PropType<any[]>,
      default: () => []
    },
    isVipRoute: { // 区分普通路口和VIP路口，如果是vip路口（特勤路线和分组管控），才需要获取通道和处理通道冲突，并显示已选方向
      type: Boolean,
      default: false
    },
    clickMode: { // 是否开启点击模式
      type: Boolean,
      default: false
    },
    isThirdSignal: {
      type: Boolean,
      default: false
    },
    channelType: {
      type: Boolean,
      default: false
    }
  },
  emits: ['handleClickCrossIcon'],
  data() {
    return {
      reset: false,
      bodyDomWidth: 352,
      bodyDomSize: {
        width: 1920,
        height: 1080
      } as BodyDomSize,
      resizeHandler: null as (() => void) | null
    }
  },
  watch: {
    '$route': {
      handler(val: any, oldVal: any) {
        if (val.query !== undefined && val.query.agentid !== undefined) {
          this.resetCrossDiagram()
        }
      },
      // 深度观察监听
      deep: true
    },
    isVipRoute: {
      handler(val: boolean, oldVal: boolean) {
        if (val) {
          this.reset = true
        }
      }
    },
    channelType: {
      handler(val: boolean, oldVal: boolean) {
        if (val) {
          this.reset = true
        }
      }
    },
    devId: {
      handler(val1: string, val2: string) {
        if (val1 !== val2 && val2 !== undefined) {
          this.resetCrossDiagram()
        }
      }
    }
  },
  created() {
    const route = useRoute()
    if (route.query !== undefined && Object.keys(route.query).length && route.query.agentid !== undefined) {
      this.resetCrossDiagram()
    }
  },
  mounted() {
    this.getParentSize()
    this.reset = true
  },
  updated() {
  },
  unmounted() {
    // 清理事件监听器
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler)
    }
  },
  methods: {
    resetCrossDiagram() {
      this.reset = false
      nextTick(() => {
        this.reset = true
      })
    },
    getParentSize() {
      // 获取最外层dom尺寸，适配准备
      const _this = this
      nextTick(() => {
        const el = _this.$el as HTMLElement
        if (el.parentElement === null || el.parentElement === undefined) return
        _this.bodyDomSize.width = el.parentElement.clientWidth
        _this.bodyDomWidth = _this.bodyDomSize.width
        console.log(_this.bodyDomWidth)

        // 创建resize处理函数并保存引用
        _this.resizeHandler = () => {
          // 定义窗口大小变更通知事件
          const currentEl = _this.$el as HTMLElement
          if (currentEl.parentElement === null || currentEl.parentElement === undefined) return
          _this.bodyDomSize.width = currentEl.parentElement.clientWidth
          _this.bodyDomWidth = _this.bodyDomSize.width
          console.log(_this.bodyDomWidth)
          console.log('resize this.bodyDomSize.width', _this.bodyDomSize.width)
        }

        window.addEventListener('resize', _this.resizeHandler, false)
      })
    },
    handleClickCrossIcon(allChoosedDir: any, curClickedPhase: any) {
      this.$emit('handleClickCrossIcon', allChoosedDir, curClickedPhase)
    }
  }
})
</script>
