<template>
  <div class="crossImg">
    <div v-show="isShowState">
      <div class="stateText">
        <div style="border:0px solid red;float:right;">
          {{ stateName }}
        </div>
      </div>
    </div>
    <div v-show="isShowMode">
      <div class="controlText">
        <div style="border:0px solid red;float:right;">
          {{ controlName }}
        </div>
      </div>
      <div class="modeText">
        {{ modeName }}
      </div>
    </div>
    <!-- 右行道路 B-->
    <div class="right-dir-road" v-if="roadDir === 'right'">
      <div class="centerText" v-if="crossType !== 'Customroads' && isHasPhase">
        <div class="merge-direction-icon">
          <XRDDirSelector
          :Data="xdrMData"
          Width="80px"
          Height="80px"
          Widths="80px"
          Heights="80px"
          :showlist="mergeShowList"/>
        </div>
      <!-- 相位倒计时 -->
      <div class="phaseCountdown" v-show="isShowInterval" v-if="isLoaded && isHasPhase && isHasCountdown && contrloType === 'ring'" :class="{'countdownBg': isLoaded}">
        <div v-for="curPhase in phaseCountdownList" :key="curPhase.id" :style="{color: curPhase.phaseCountdownColor}">
          <div v-if="curPhase.phaseCountdown !== undefined && curPhase.phaseCountdown !== -1" style="overflow: hidden;margin-bottom: 4px;">
            <span style="float: left;width: 42px;height: 42px;position: relative;border: 1px solid rgba(255, 255, 255, 0.1);margin-right: 5px;">
              <XRDDirSelector :Data="xdrData" :Datas="xdrpedData" Width="55px" Height="55px" Widths="55px" Heights="55px" :showlist="curPhase.showlist" :roadDirection="roadDirection"></XRDDirSelector>
            </span>
            <span style="float: left;color: #fff;margin-right: 8px;width: 30px;">P{{ curPhase.id }}:</span>
            <span style="float: left;font-size: 25px;">{{ curPhase.phaseCountdown }}</span>
          </div>
        </div>
      </div>
      <!-- 阶段倒计时 -->
      <div class="phaseCountdown" v-show="isShowInterval" v-if="isLoaded && isHasStageCountdown && contrloType === 'stage'" :class="{'countdownBg': isLoaded}">
          <div style="overflow: hidden;margin-bottom: 4px;" :style="{color: stageCountdownList[0].phaseCountdownColor}">
            <span style="float: left;width: 42px;height: 42px;position: relative;border: 1px solid rgba(255, 255, 255, 0.1);margin-right: 5px;">
              <XRDDirSelector :Data="xdrData" :Datas="xdrpedData" Width="55px" Height="55px" Widths="55px" Heights="55px" :showlist="dirListSetTheme(stageCountdownList)" :roadDirection="roadDirection"></XRDDirSelector>
            </span>
            <span style="float: left;color: #fff;margin-right: 8px;width: 30px;">S{{ crossStatusData.current_stage }}:</span>
            <span style="float: left;font-size: 25px;">{{ crossStatusData.current_stagecd }}</span>
          </div>
      </div>
      <!-- 手动刷新 -->
      <div v-if="!isLoaded">
        <RefreshSvg @click="refresh"/>
        <span class="text">重新获取路口图</span>
      </div>
    </div>
      <!-- 路口底图 -->
      <div class="baseImg">
        <!-- 城市道路 -->
        <CrossRoadsSvg v-if="crossType === 'Crossroads'"/>
        <TShapeEastRoadsSvg v-if="crossType === 'TypeT-east'"/>
        <TShapeWestRoadsSvg v-if="crossType === 'TypeT-west'"/>
        <TShapeNorthRoadsSvg v-if="crossType === 'TypeT-north'"/>
        <TShapeSouthRoadsSvg v-if="crossType === 'TypeT-south'"/>
        <!-- 其他路口 -->
        <CustomRoadsSvg v-if="crossType === 'Customroads'"/>
        <!-- 匝道 -->
        <RampEastRoadsSvg v-if="crossType === 'ramp-east' && !isVipRoute" />
        <RampWestRoadsSvg v-if="crossType === 'ramp-west' && !isVipRoute" />
        <RampNorthRoadsSvg v-if="crossType === 'ramp-north' && !isVipRoute" />
        <RampSouthRoadsSvg v-if="crossType === 'ramp-south' && !isVipRoute" />
        <!-- 路段行人过街 -->
        <PedSectionSNSvg v-if="crossType === 'ped-section-south-north'" />
        <PedSectionEWSvg v-if="crossType === 'ped-section-east-west'" />
      </div>
      <!-- 城市道路状态-->
      <div v-if="mainType === '100' || mainType === '101' || mainType === '104'">
        <!-- 人行道 -->
        <div class="sidewalk" id="sidewalk" v-if="resetflag && isLoaded">
          <SidewalkClickSvg v-if="isVipRoute && compSidewalkPhaseData.length" :Data="compSidewalkPhaseData" :clickMode="clickMode" @handleClickSidewalkIcon="handleClickSidewalkIcon" />
          <SidewalkSvg v-else v-for="(side, index) in compSidewalkPhaseData" :key="side.key + '-' + index" :Data="side" :crossType="crossType" />
        </div>
        <!-- 车道相位 -->
        <div v-if="resetflag" class="phaseIcon">
          <PhaseIconSvg v-for="(item, index) in compLanePhaseData" :key="item.key + '-' + index" :Data="item" :isVipRoute="isVipRoute" :clickMode="clickMode" @handleClickPhaseIcon="handleClickPhaseIcon" />
        </div>
         <!-- 公交相位 -->
        <div v-if="resetflag && !isVipRoute" class="busIcon">
          <BusMapSvg v-for="(item, index) in comdireBusPhaseData" :key="'Busmap-' + item.key + '-' + index" :Data="item" />
          <PhaseIconSvg v-for="(item, index) in comdireBusPhaseData" :key="item.key + '-' + index" :Data="item"/>
        </div>
        <!-- 有效方向 -->
        <div v-if="isVipRoute" class="effectDir">
          <PhaseIconSvg v-for="(item, index) in effectDirData" :key="item.key + '-' + index" :Data="item" />
        </div>
      </div>
      <!-- 匝道状态 -->
        <!-- 车道相位 -->
      <div v-if="resetflag && mainType === '103' && !isVipRoute">
        <RampPhaseIconSvg v-for="(item, index) in LanePhaseData" :key="item.key + '-' + index" :Data="item" />
      </div>
    </div>
    <!-- 右行道路 E-->

    <!-- 左行道路 B-->
    <div class="left-dir-road" v-if="roadDir === 'left'">
      <div class="centerText" v-if="crossType !== 'Customroads' && isHasPhase">
        <!-- 相位倒计时 -->
        <div class="phaseCountdown" v-show="isShowInterval" v-if="isLoaded && isHasPhase && isHasCountdown && contrloType === 'ring'" :class="{'countdownBg': isLoaded}">
          <div v-for="curPhase in phaseCountdownList" :key="curPhase.id" :style="{color: curPhase.phaseCountdownColor}">
            <div v-if="curPhase.phaseCountdown !== undefined && curPhase.phaseCountdown !== -1" style="overflow: hidden;margin-bottom: 4px;">
              <span style="float: left;width: 42px;height: 42px;position: relative;border: 1px solid rgba(255, 255, 255, 0.1);margin-right: 5px;">
                <XRDDirSelector :Data="xdrData" :Datas="xdrpedData" Width="55px" Height="55px" Widths="55px" Heights="55px" :showlist="curPhase.showlist" :roadDirection="roadDirection"></XRDDirSelector>
              </span>
              <span style="float: left;color: #fff;margin-right: 8px;width: 30px;">P{{ curPhase.id }}:</span>
              <span style="float: left;font-size: 25px;">{{ curPhase.phaseCountdown }}</span>
              </div>
            </div>
        </div>
        <!-- 阶段倒计时 -->
      <div class="phaseCountdown" v-show="isShowInterval" v-if="isLoaded && isHasStageCountdown && contrloType === 'stage'" :class="{'countdownBg': isLoaded}">
          <div style="overflow: hidden;margin-bottom: 4px;" :style="{color: stageCountdownList[0].phaseCountdownColor}">
            <span style="float: left;width: 42px;height: 42px;position: relative;border: 1px solid rgba(255, 255, 255, 0.1);margin-right: 5px;">
              <XRDDirSelector :Data="xdrData" :Datas="xdrpedData" Width="55px" Height="55px" Widths="55px" Heights="55px" :showlist="dirListSetTheme(stageCountdownList)" :roadDirection="roadDirection"></XRDDirSelector>
            </span>
            <span style="float: left;color: #fff;margin-right: 8px;width: 30px;">S{{ crossStatusData.current_stage }}:</span>
            <span style="float: left;font-size: 25px;">{{ crossStatusData.current_stagecd }}</span>
          </div>
      </div>
        <!-- 手动刷新 -->
        <div v-if="!isLoaded">
          <RefreshSvg @click="refresh"/>
          <span class="text">重新获取路口图</span>
        </div>
      </div>
      <!-- 路口底图 -->
      <div class="baseImg">
        <!-- 城市道路 -->
        <LCrossRoadsSvg v-if="crossType === 'Crossroads'"/>
        <LTShapeEastRoadsSvg v-if="crossType === 'TypeT-east'"/>
        <LTShapeWestRoadsSvg v-if="crossType === 'TypeT-west'"/>
        <LTShapeNorthRoadsSvg v-if="crossType === 'TypeT-north'"/>
        <LTShapeSouthRoadsSvg v-if="crossType === 'TypeT-south'"/>
        <!-- 其他路口 -->
        <CustomRoadsSvg v-if="mainType !== '100' && mainType !== '101'"/>
      </div>
      <!-- 城市道路状态-->
      <div v-if="mainType === '100' || mainType === '101'">
        <!-- 人行道 -->
        <div class="sidewalk" v-if="resetflag && isLoaded">
          <SidewalkClickSvg v-if="isVipRoute && compSidewalkPhaseData.length" :Data="compSidewalkPhaseData" :clickMode="clickMode" @handleClickSidewalkIcon="handleClickSidewalkIcon" />
          <SidewalkSvg v-else v-for="side in compSidewalkPhaseData" :key="side.key" :Data="side" :crossType="crossType" />
        </div>
        <!-- 车道相位 -->
        <div v-if="resetflag" class="phaseIcon">
          <LPhaseIconSvg v-for="item in compLanePhaseData" :key="item.key" :Data="item" :isVipRoute="isVipRoute" :clickMode="clickMode" @handleClickPhaseIcon="handleClickPhaseIcon"/>
        </div>
         <!-- 公交相位 -->
        <div v-if="resetflag" class="busIcon">
          <BusMapSvg v-for="(item, index) in comdireBusPhaseData" :key="'Busmap-' + item.key + '-' + index" :Data="item" />
          <LPhaseIconSvg v-for="(item, index) in comdireBusPhaseData" :key="item.key + '-' + index" :Data="item"/>
        </div>
      </div>
    </div>
    <!-- 左行道路 E-->
  </div>
</template>
<script lang="ts">
import { defineComponent, nextTick } from 'vue';
import type { PropType } from 'vue';
import XRDDirSelector from '@/components/XRDDirSelector';
import PhaseIconSvg from './phaseIcon/phaseIconSvg';
import PhaseDataModel from '@/components/PhaseDataModel';
import { uploadSingleTscParam } from './utils';
import CrossRoadsSvg from './baseImg/CrossRoadsSvg';
import TShapeEastRoadsSvg from './baseImg/TShapeEastRoadsSvg';
import TShapeWestRoadsSvg from './baseImg/TShapeWestRoadsSvg.vue';
import TShapeNorthRoadsSvg from './baseImg/TShapeNorthRoadsSvg.vue';
import TShapeSouthRoadsSvg from './baseImg/TShapeSouthRoadsSvg.vue';
import CustomRoadsSvg from './baseImg/CustomRoadsSvg.vue';
import RefreshSvg from './baseImg/refreshSvg';
import SidewalkSvg from './baseImg/SidewalkSvg';
import SidewalkClickSvg from './baseImg/SidewalkClickSvg';
import RampEastRoadsSvg from './baseImg/RampEastSvg';
import RampWestRoadsSvg from './baseImg/RampWestSvg';
import RampNorthRoadsSvg from './baseImg/RampNorthSvg';
import RampSouthRoadsSvg from './baseImg/RampSouthSvg';
import RampPhaseIconSvg from './phaseIcon/rampPhaseIconSvg';
import PedSectionEWSvg from './baseImg/PedSectionEWSvg';
import PedSectionSNSvg from './baseImg/PedSectionSNSvg';
import LCrossRoadsSvg from './baseImg/leftroad/LCrossRoadsSvg';
import LTShapeEastRoadsSvg from './baseImg/leftroad/LTShapeEastRoadsSvg';
import LTShapeWestRoadsSvg from './baseImg/leftroad/LTShapeWestRoadsSvg.vue';
import LTShapeNorthRoadsSvg from './baseImg/leftroad/LTShapeNorthRoadsSvg.vue';
import LTShapeSouthRoadsSvg from './baseImg/leftroad/LTShapeSouthRoadsSvg.vue';
import LPhaseIconSvg from './phaseIcon/LphaseIconSvg';
import CrossDiagramMgr from '@/components/EdgeMgr/controller/crossDiagramMgr';
import BusMapSvg from './busIcon/busMapSvg';
import {
  getValidDirections,
  getMergeDirections,
  getMessageByCode,
  getShowPedDirection,
  getEffectDirectionPositions,
  handleGetValidDirections,
  handleGetMergeDirections,
  isArraysEqual,
  getCrossType,
  createPhaseStatusMap,
  setDefaultColor,
  setYellowFlashStatus,
  setSpecialControlColor,
  processPhaseStatus,
  processPedStatus,
  removeDuplicateDirections,
  setDirectionListTheme,
  handleRepeatRealdir,
  createChannelStatusMap,
  processChannelPhaseStatus,
  processChannelPedStatus
} from './utils';
import CrossDirectionConflictList from './conflictList';
import RingDataModel from './RingDataModel';
import type {
  CrossStatusData,
  CrossInfo,
  PhaseCountdownItem,
  PhaseStatusItem,
  OverlapStatusItem,
  LanePhaseDataItem,
  SidewalkPhaseDataItem,
  BusPhaseDataItem,
  ChannelStatusItem,
  EffectDirDataItem,
  XdrDataItem,
  RoadDirection,
  CrossType,
  MainType,
  ControlType
} from './types';
import { getSignalManagement } from '@/api/business/signalManagement';

export default defineComponent({
  name: 'crossDiagram',
  components: {
    XRDDirSelector,
    PhaseIconSvg,
    CrossRoadsSvg,
    TShapeEastRoadsSvg,
    TShapeWestRoadsSvg,
    TShapeNorthRoadsSvg,
    TShapeSouthRoadsSvg,
    CustomRoadsSvg,
    RefreshSvg,
    SidewalkSvg,
    RampEastRoadsSvg,
    RampWestRoadsSvg,
    RampNorthRoadsSvg,
    RampSouthRoadsSvg,
    RampPhaseIconSvg,
    PedSectionEWSvg,
    PedSectionSNSvg,
    LCrossRoadsSvg,
    LTShapeEastRoadsSvg,
    LTShapeWestRoadsSvg,
    LTShapeNorthRoadsSvg,
    LTShapeSouthRoadsSvg,
    LPhaseIconSvg,
    BusMapSvg,
    SidewalkClickSvg
  },
  props: {
    crossStatusData: {
      type: Object as PropType<CrossStatusData>,
      required: false
    },
    devId: {
      type: Number,
      required: true
    },
    isShowInterval: {
      type: Boolean,
      default: true
    },
    isShowMessage: {
      type: Boolean,
      default: true
    },
    roadDirection: {
      type: String as PropType<RoadDirection>,
      required: true
    },
    isShowState: {
      type: Boolean,
      default: false
    },
    isShowMode: {
      type: Boolean,
      default: false
    },
    modeName: {
      type: String,
      default: ''
    },
    controlName: {
      type: String,
      default: ''
    },
    stateName: {
      type: String,
      default: ''
    },
    choosedDirection: {
      type: Array as PropType<number[]>,
      default: () => []
    },
    choosedPedDirection: {
      type: Array as PropType<number[]>,
      default: () => []
    },
    isVipRoute: { // 区分普通路口和VIP路口，如果是vip路口（特勤路线和分组管控），才需要获取通道和处理通道冲突，并显示已选方向
      type: Boolean,
      default: false
    },
    clickMode: {
      type: Boolean,
      default: false
    },
    isThirdSignal: {
      type: Boolean,
      default: false
    },
    channelType: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    tempType: {
      handler(val: string) {
        this.getCrossType();
      }
    },
    crossStatusData: {
      handler(val: CrossStatusData, oldVal: CrossStatusData) {
        // 路口状态数据
        if (!this.channelType || (this.channelType && this.isThirdSignal)) {
          this.compareIsChangedPhase(val, oldVal); // 比较相位状态决定是否更新相位图标（解决虚相位显示问题，虚相位图标不显示）
        }
        this.statusData = JSON.parse(JSON.stringify(val));
        // 默认显示相位数据（包括黄闪、全红、关灯状态下，或者匝道，均不做比对跟随相位的处理）
        this.drawDefaultPhaseIcon();
        if (this.channelType && !this.isThirdSignal) {
          // 通道路口图状态数据（实时通道展示排除第三方信号机，第三方信号机还是按模版路口一样展示相位状态）
          this.channelStatusList = val.channellamp || [];
          this.createChannelStatusMap();
          this.getChannelPhaseStatus();
          this.getChannelPedStatus();
          return;
        }
        this.handleTempCrossStatus(val);
      },
      // 深度观察监听
      deep: true
    },
    devId: {
      handler(val1: string, val2: string) {
        if (val1 !== val2) {
          this.init();
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      roadDir: 'right' as RoadDirection, // 道路行车方向，默认右行
      phaseCountdownList: [] as PhaseCountdownItem[], // 相位倒计时列表
      stageCountdownList: [] as PhaseCountdownItem[], // 阶段倒计时列表
      statusData: null as CrossStatusData | null, // 信号机状态
      LanePhaseData: [] as LanePhaseDataItem[], // 车道相位数据
      overlapLanePhaseData: [] as LanePhaseDataItem[], // 车道跟随相位数据
      curPhase: [] as number[], // 当前相位列表
      phaseStatusList: [] as PhaseStatusItem[], // 相位状态列表
      phaseStatusMap: new Map<number, any>(), // 相位状态映射
      overlapPhaseStatusMap: new Map<number, any>(), // 跟随相位状态映射
      ColorMap: new Map([[0, '#828282'], [1, '#ff2828'], [2, '#f7b500'], [3, '#77fb65'], [4, '#77fb65'], [5, '#f7b500']]), // 当前相位状态 --- 0：关灯, 1：红, 2：黄,  3：绿, 4：绿闪, 5：黄闪
      SidewalkColorMap: new Map([[0, '#828282'], [1, '#e24b4b'], [3, '#7bd66b']]),
      tempType: '' as string, // 模版类型
      mainType: '101' as MainType, // 路口形状
      mainDirection: '000' as string, // 路口方向
      crossType: '' as CrossType, // 路口底图类型
      isLoaded: false as boolean, // 是否成功加载底图
      isHasPhase: true as boolean, // 是否有相位状态数据
      phaseControlColorMap: new Map([['全红', '#ff2828'], ['关灯', '#828282'], ['默认', '#fff'], ['方向锁定', '#fff']]),
      sidewalkPhaseData: [] as SidewalkPhaseDataItem[], // 行人相位
      overlapsidewalkPhaseData: [] as SidewalkPhaseDataItem[], // 行人跟随相位
      resetflag: true as boolean, // 离线后，控制行人相位、车道相位reset标识
      compLanePhaseData: [] as LanePhaseDataItem[], // 对比车道相位和车道跟随相位后，显示的数据
      compSidewalkPhaseData: [] as SidewalkPhaseDataItem[], // // 对比行人相位和车道跟随相位后，显示的数据
      comdirePhaseData: [] as LanePhaseDataItem[], // 对比相同方向车道相位数据后，被删减的唯一direction的数组
      comdireOverlapPhaseData: [] as LanePhaseDataItem[], // 对比相同方向车道跟随相位数据后，被删减的唯一direction的数组
      busPhaseData: [] as BusPhaseDataItem[], // 公交相位数据
      comdireBusPhaseData: [] as BusPhaseDataItem[], //  对比相同方向公交车道数据后，被删减的唯一direction的数组
      channelStatusMap: new Map<number, any>(), // 通道实时状态映射
      channelStatusList: [] as ChannelStatusItem[], // 通道实时状态列表
      phaseDirMap: new Map<number, any>(),
      xdrData: {
        left: '5px',
        top: '4px'
      } as XdrDataItem,
      xdrpedData: {
        left: '1px',
        top: '1px'
      } as XdrDataItem,
      isHasCountdown: false as boolean,
      isHasStageCountdown: false as boolean,
      contrloType: 'ring' as ControlType,
      isMphaseStatusDataReturnMap: new Map<number, boolean>(), // 每个跟随相位的母相位是否返回了相位状态数据
      overlapStatusCompareSum: 0 as number, // 首次加载跳过比较母相位状态数据是否返回
      effectDirData: [] as EffectDirDataItem[], // 路口有效方向
      mergeShowList: [2, 4, 10] as number[],
      xdrMData: {
        left: '18px',
        top: '18px'
      } as XdrDataItem,
      // 添加缺失的属性
      CrossDiagramMgr: null as CrossDiagramMgr | null,
      PhaseDataModel: null as PhaseDataModel | null,
      crossInfo: null as CrossInfo | null,
      overlapStatusList: [] as OverlapStatusItem[],
      curStage: 0 as number,
      allPatternList: [] as any[],
      channelList: [] as any[],
      sidewalkDir: [] as any[],
      effectiveDirection: [] as number[],
      inneChoosedDirection: [] as number[],
      inneChoosedPedDirection: [] as number[],
      isShowCurrentStage: false as boolean,
      currentStage: 0 as number
    };
  },
  methods: {
    compareIsChangedPhase(newCrossStatus: CrossStatusData, oldCrossStatus: CrossStatusData): void {
      if (newCrossStatus.control === 1 || newCrossStatus.control === 2 || newCrossStatus.control === 3) return;
      // 返回的相位状态改变后，按照返回的相位状态更新路口相位显示（下个周期生效）
      const newPhaseIds = newCrossStatus.phase?.map(item => item.id) || [];
      const oldPhaseIds = oldCrossStatus.phase?.map(item => item.id) || [];
      if (!this.isArraysEqual(newPhaseIds, oldPhaseIds)) { // 通过比较相位id是否完全一致
        this.getIntersectionInfo();
      }
    },
    // 使用工具函数替代
    isArraysEqual: isArraysEqual,
    handleTempCrossStatus(val: CrossStatusData): void {
      // 模版路口图状态数据
      this.phaseStatusList = val.phase || [];
      this.overlapStatusList = val.overlap || [];
      if (val.control === 1 || val.control === 2 || val.control === 3) {
        // 黄闪、全红、关灯属于特殊控制，优先级最高，直接改变灯色，不用判断phase里的type，也不需要考虑跟随相位的灯色优先级
        if (val.control === 1) {
          this.getYellowFlashColor();
        } else {
          this.SpecialControl(val);
        }
        this.isHasPhase = false;
        return;
      }
      if (!val.phase && !this.overlapStatusList.length) {
        // 非特殊控制，相位和跟随相位不存在的情况下，灯色恢复默认
        this.handleSpecialControlStatus('默认');
        this.isHasPhase = false;
        return;
      }
      if (val.control === 16) {
        // 方向锁定时，相位状态显示与通道无关，显示接口返回的红灯会有歧义，此处特殊处理显示默认白色
        this.handleSpecialControlStatus('方向锁定');
        this.isHasPhase = false;
        return;
      }
      this.curPhase = val.current_phase || [];
      this.curStage = val.current_stage || 0;
      this.isHasPhase = true;
      if (val.phase) {
        this.createPhaseStatusMap();
      }
      if (val.overlap) {
        this.createOverlapPhaseStatusMap();
      }
      // 正常情况下，获取车道相位、车道跟随相位、相位倒计时、行人相位、行人跟随相位 的状态
      this.getPhaseStatus();
      this.getOverlapPhaseStatus();
      this.getCurPhaseCountdown();
      this.getCurStageCountdown();
      this.getBusPhaseStatus();
      if (this.mainType === '100' || this.mainType === '101' || this.mainType === '104') {
        // 城市道路和路段行人过街才显示人行道状态
        this.getpedStatus();
        this.getOverlapPedStatus();
        // 算法对比行人相位与行人跟随相位的状态
        this.comparePedStatus();
        // 算法对比车道相位与车道跟随相位的状态
        this.comparePhaseStatus();
      }
    },
    init(): void {
      this.CrossDiagramMgr = new CrossDiagramMgr();
      this.getRoadDirection();
      this.PhaseDataModel = new PhaseDataModel(this.roadDirection);
      this.getIntersectionInfo(); // 获取路口信息
    },
    drawDefaultPhaseIcon(): void {
      if (!this.CrossDiagramMgr) return;
      this.compLanePhaseData = JSON.parse(JSON.stringify(this.CrossDiagramMgr.compare(this.LanePhaseData, this.overlapLanePhaseData, 'type', 'nostatus')));
      this.compSidewalkPhaseData = JSON.parse(JSON.stringify(this.CrossDiagramMgr.compare(this.sidewalkPhaseData, this.overlapsidewalkPhaseData, 'pedtype', 'nostatus')));
      this.comdireBusPhaseData = JSON.parse(JSON.stringify(this.busPhaseData));
      console.log(this.LanePhaseData);
      console.log(this.overlapLanePhaseData);
      console.log('###################', this.compLanePhaseData);
    },
    comparePhaseStatus(): void {
      if (!this.CrossDiagramMgr) return;
      // 对比车道： 跟随相位和相位的状态数据（此处判断是为了保证被比较的数据direction都是唯一的）
      if (!this.comdirePhaseData.length && !this.comdireOverlapPhaseData.length) {
        this.compLanePhaseData = this.CrossDiagramMgr.compare(this.LanePhaseData, this.overlapLanePhaseData, 'type');
      } else if (!this.comdireOverlapPhaseData.length) {
        this.compLanePhaseData = this.CrossDiagramMgr.compare(this.comdirePhaseData, this.overlapLanePhaseData, 'type');
      } else if (!this.comdirePhaseData.length) {
        this.compLanePhaseData = this.CrossDiagramMgr.compare(this.LanePhaseData, this.comdireOverlapPhaseData, 'type');
      } else {
        this.compLanePhaseData = this.CrossDiagramMgr.compare(this.comdirePhaseData, this.comdireOverlapPhaseData, 'type');
      }
    },
    comparePedStatus(): void {
      if (!this.CrossDiagramMgr) return;
      // 对比人行道： 跟随相位和相位的状态数据
      this.compSidewalkPhaseData = this.CrossDiagramMgr.compare(this.sidewalkPhaseData, this.overlapsidewalkPhaseData, 'pedtype');
    },
    resetPhaseStatus(): void {
      // 车道相位、行人相位恢复默认状态
      this.resetflag = false;
      nextTick(() => {
        this.resetflag = true;
      });
    },
    SpecialControl(data: CrossStatusData): void {
      switch (data.control) {
        case 2:
          this.handleSpecialControlStatus('全红');
          break;
        case 3:
          this.handleSpecialControlStatus('关灯');
          break;
        default:
          this.handleSpecialControlStatus('默认');
      }
    },
    handleDefaultStatus(): void {
      // 使用工具函数恢复默认状态
      if (this.compLanePhaseData.length) {
        this.compLanePhaseData = setDefaultColor(this.compLanePhaseData);
      }
      if (this.compSidewalkPhaseData.length) {
        this.compSidewalkPhaseData = setDefaultColor(this.compSidewalkPhaseData);
      }
      if (this.comdireBusPhaseData.length) {
        this.comdireBusPhaseData = setDefaultColor(this.comdireBusPhaseData);
      }
      this.phaseCountdownList = [];
      this.stageCountdownList = [];
      this.resetPhaseStatus();
    },
    handleSpecialControlStatus(Control: string): void {
      this.resetPhaseStatus();
      // 控制黄闪、全红、关灯、默认情况下的车道相位颜色和倒计时颜色
      if (Control === '默认') {
        // 倒计时恢复默认颜色
        this.phaseCountdownList.forEach(item => {
          item.phaseCountdown = 0;
          item.id = 0;
          item.phaseCountdownColor = '#fff';
        });
      }
      // 使用工具函数设置特殊控制状态颜色
      if (this.compLanePhaseData.length) {
        this.compLanePhaseData = JSON.parse(JSON.stringify(
          setSpecialControlColor(this.compLanePhaseData, this.phaseControlColorMap, Control)
        ));
      }
      if (this.compSidewalkPhaseData.length) {
        this.compSidewalkPhaseData = JSON.parse(JSON.stringify(
          setSpecialControlColor(this.compSidewalkPhaseData, this.phaseControlColorMap, Control)
        ));
      }
      if (this.comdireBusPhaseData.length) {
        this.comdireBusPhaseData = JSON.parse(JSON.stringify(
          setSpecialControlColor(this.comdireBusPhaseData, this.phaseControlColorMap, Control)
        ));
      }
      if (this.mainType === '103') {
        if (this.LanePhaseData.length) {
          this.LanePhaseData = JSON.parse(JSON.stringify(
            setSpecialControlColor(this.LanePhaseData, this.phaseControlColorMap, Control)
          ));
        }
      }
    },
    createPhaseStatusMap(): void {
      // 使用工具函数生成相位id和相位状态对应数据结构
      this.phaseStatusMap = createPhaseStatusMap(this.phaseStatusList);
    },
    getYellowFlashColor(): void {
      // 使用工具函数设置黄闪状态
      this.compLanePhaseData = JSON.parse(JSON.stringify(
        setYellowFlashStatus(this.compLanePhaseData, 'type')
      ));

      this.compSidewalkPhaseData = JSON.parse(JSON.stringify(
        setYellowFlashStatus(this.compSidewalkPhaseData, 'pedtype')
      ));

      this.comdireBusPhaseData = JSON.parse(JSON.stringify(
        setYellowFlashStatus(this.comdireBusPhaseData, 'type')
      ));

      if (this.mainType === '103') {
        // 匝道相位
        if (this.LanePhaseData.length) {
          this.LanePhaseData = JSON.parse(JSON.stringify(
            setYellowFlashStatus(this.LanePhaseData, 'type')
          ));
        }
      }
    },
    getPhaseStatus(): void {
      if (!this.CrossDiagramMgr) return;
      // 使用工具函数得到车道相位状态（颜色）
      this.comdirePhaseData = [];
      const curLanePhaseData = processPhaseStatus(
        this.LanePhaseData,
        this.phaseStatusMap,
        this.ColorMap,
        'phase'
      );
      this.LanePhaseData = JSON.parse(JSON.stringify(curLanePhaseData));
      // 处理相位数据中，方向direction重复的情况：相同direction下，按照状态的优先级显示该方向的灯色：绿灯(3) > 绿闪(4) > 黄灯(2) > 红灯(1)
      // 如果有相同direction，处理后会改变原数组长度，导致第二次无法正确比较状态，因此需要中间变量存储
      this.comdirePhaseData = JSON.parse(JSON.stringify(this.CrossDiagramMgr.compareRepeatDirection(this.LanePhaseData, 'type', 'phase')));
    },
    createOverlapPhaseStatusMap(): void {
      // 处理跟随相位的母相位设置为忽略相位后的情况（如果跟随相位的母相位包含忽略相位，过滤掉跟随相位的状态数据，参照相位状态数据处理）
      let isMphaseStatusDataReturn = false;
      let reset = false;
      this.overlapStatusList = this.overlapStatusList.filter(ele => {
        const phaseids = this.phaseStatusList.map(item => item.id);
        const mphase = ele.mphase;
        console.log(phaseids);
        console.log(mphase);
        const phaseidsSet = new Set(phaseids);
        isMphaseStatusDataReturn = mphase.every(value => phaseidsSet.has(value));
        console.log(`跟随相位${ele.id}的母相位状态数据是否均返回?`, isMphaseStatusDataReturn);
        if (isMphaseStatusDataReturn !== this.isMphaseStatusDataReturnMap.get(ele.id) && !reset && this.overlapStatusCompareSum > 0) {
          this.getIntersectionInfo();
          reset = true;
        }
        this.isMphaseStatusDataReturnMap.set(ele.id, isMphaseStatusDataReturn);
        return isMphaseStatusDataReturn;
      });
      this.overlapStatusCompareSum = this.overlapStatusCompareSum + 1;
      // 得到跟随相位状态Map数据
      this.overlapPhaseStatusMap = new Map();
      this.overlapStatusList.forEach(phase => {
        const phaseId = phase.id;
        const phaseInfo = {
          type: phase.type,
          phaseCountdown: phase.countdown,
          pedtype: phase.pedtype
        };
        this.overlapPhaseStatusMap.set(phaseId, phaseInfo);
      });
    },
    getOverlapPhaseStatus(): void {
      if (!this.CrossDiagramMgr) return;
      // 使用工具函数得到车道跟随相位状态（颜色）
      this.comdireOverlapPhaseData = [];
      const curLanePhaseData = processPhaseStatus(
        this.overlapLanePhaseData,
        this.overlapPhaseStatusMap,
        this.ColorMap,
        'overlapphase'
      );
      this.overlapLanePhaseData = JSON.parse(JSON.stringify(curLanePhaseData));
      // 处理跟随相位数据中，方向direction重复的情况：相同direction下，按照状态的优先级显示该方向的灯色：绿灯(3) > 绿闪(4) > 黄灯(2) > 红灯(1)
      // 如果有相同direction，处理后会改变原数组长度，导致第二次无法正确比较状态，因此需要中间变量存储
      this.comdireOverlapPhaseData = JSON.parse(JSON.stringify(this.CrossDiagramMgr.compareRepeatDirection(this.overlapLanePhaseData, 'type', 'overlapphase')));
    },
    getBusPhaseStatus(): void {
      if (!this.CrossDiagramMgr) return;
      // 使用工具函数得到公交车道相位状态（颜色）
      this.comdireBusPhaseData = [];
      const curLanePhaseData = processPhaseStatus(
        this.busPhaseData,
        this.phaseStatusMap,
        this.ColorMap,
        'busphase'
      );
      this.busPhaseData = JSON.parse(JSON.stringify(curLanePhaseData));
      // 处理相位数据中，方向direction重复的情况：相同direction下，按照状态的优先级显示该方向的灯色：绿灯(3) > 绿闪(4) > 黄灯(2) > 红灯(1)
      // 如果有相同direction，处理后会改变原数组长度，导致第二次无法正确比较状态，因此需要中间变量存储
      this.comdireBusPhaseData = JSON.parse(JSON.stringify(this.CrossDiagramMgr.compareRepeatDirection(this.busPhaseData, 'type', 'busphase')));
    },
    getCurPhaseCountdown () {
      // 获取当前相位倒计时颜色
      this.phaseCountdownList = []
      this.isHasCountdown = false
      this.curPhase.forEach(curP => {
        this.phaseStatusList.forEach(phaseInfo => {
          if (phaseInfo.id === curP) {
            let countdownObj = {}
            countdownObj.id = phaseInfo.id
            countdownObj.phaseCountdown = phaseInfo.countdown
            countdownObj.phaseCountdownColor = this.ColorMap.get(phaseInfo.type)
            let curphasedir = this.phaseDirMap.get(phaseInfo.id)
            if (curphasedir !== undefined) {
              if (curphasedir.direction && curphasedir.direction.length > 0) {
                countdownObj.showlist = curphasedir.direction.map(dir => {
                return {
                  id: dir,
                  peddirection: getShowPedDirection(curphasedir.peddirection, this.PhaseDataModel!),
                  color: '#fff'
                }
              })
              } else {
                countdownObj.showlist = [
                  {
                  id: '',
                  peddirection: getShowPedDirection(curphasedir.peddirection, this.PhaseDataModel!),
                  color: '#fff'
                  }
                ]
              }
            } else {
              countdownObj.showlist = []
            }

            this.phaseCountdownList.push(countdownObj)
            if (phaseInfo.countdown !== undefined && phaseInfo.countdown !== -1) {
              this.isHasCountdown = true
            }
          }
        })
      })
    },
    stagesList () {
      let list = []
      this.isHasStageCountdown = false
      if (this.crossStatusData && this.crossStatusData.stages && this.crossInfo.phaseList && this.crossInfo.phaseList.length > 0) {
        this.isHasStageCountdown = true
        let ringDataModel = new RingDataModel(this.crossStatusData, this.crossInfo.phaseList)
        list = ringDataModel.getStageData()
        if (this.isShowCurrentStage) {
          this.currentStage = this.crossStatusData.current_stage
        }
      }
      return list
    },
    dirListSetTheme (list) {
      let dirArr = []
      for (let rec of list) {
        let recd = {
          ...rec,
          color: '#fff'
        }
        dirArr.push(recd)
        for (let i = 0; i < rec.peddirection.length; i++) {
          rec.peddirection[i].color = 'rgba(255, 255, 255, 0.6)'
        }
      }
      return dirArr
    },
    getCurStageCountdown () {
      // 获取当前相位倒计时颜色
      this.stageCountdownList = []
      let stagesList = this.stagesList()
      this.stageCountdownList = stagesList[this.curStage - 1]
      this.stageCountdownList = this.stageCountdownList.map(phaseInfo => {
        return {
          ...phaseInfo,
          phaseCountdownColor: this.ColorMap.get(phaseInfo.type)
        }
      })
      // console.log(this.stageCountdownList)
    },

    getIntersectionInfo () {
      // 获取路口信息
      this.contrloType = 'ring'
      const id = this.devId
      getSignalManagement(id).then(res => {
        if (!res.data.success) {
          this.isLoaded = false
          let commomMsg = this.$t('platform.overview.signalID') + ' : ' + id
          let msg = getMessageByCode(res.data.code, this.$i18n.locale)
          if (res.data.data) {
            // 子类型错误
            let childErrorCode = res.data.data.errorCode
            if (childErrorCode) {
              let childerror = getMessageByCode(res.data.data.errorCode, this.$i18n.locale)
              msg = msg + ' - ' + childerror
            }
          }
          msg = msg + ' - ' + commomMsg
          // this.isShowMessage && this.$message.error(msg)
          if (this.isShowMessage) {
            console.log(msg)
          }
          return
        }
        this.isLoaded = true
        this.tempType = res.data.data.type
        // 获取车道相位、行人相位信息（坐标、名称）
        this.mainType = this.tempType.split('-')[0]
        this.mainDirection = this.tempType.split('-')[1]
        if (this.isVipRoute) {
          this.handleValidDirections();
          this.handleMergeDirections();
        }
        if (this.isVipRoute && !this.isThirdSignal) {
          // 特勤、分组管控下，非第三方设备，按通道显示相位方向；第三方设备还是按路口相位配置显示相位方向
          this.getChannelInfo()
          return
        }
        if (this.channelType && !this.isThirdSignal) {
          this.getChannelInfo()
          return
        }
        this.getTempCrossInfo(res)
        // 显示阶段的判断条件
        this.allPatternList = res.data.data.param.patternList
        if (this.allPatternList[0].rings === undefined || this.allPatternList[0].rings.length === 0) {
          this.contrloType = 'stage'
        } else if (this.allPatternList[0].contrloType === 'stage') {
          this.contrloType = 'stage'
        } else {
          this.contrloType = 'ring'
        }
      })
    },
    getTempCrossInfo (res) {
      this.crossInfo = res.data.data.param
      this.crossInfo.phaseList.forEach(cross => this.phaseDirMap.set(cross.id, {direction: cross.direction, peddirection: cross.peddirection}))
      if (this.mainType === '100' || this.mainType === '101' || this.mainType === '104') {
        // 城市道路加载车道相位坐标和人行道坐标
        this.getPhasePos()
        this.getOverlapPhasePos()
        this.getPedPhasePos()
        this.getOverlapPedPhasePos()
        if (!this.isVipRoute) {
          this.getBusPos()
        }
      }
      if (!this.isVipRoute && this.mainType === '103') {
        // 获取匝道道路的主路和支路的相位坐标
        this.getRampPhasePos()
      }
      if (!this.isVipRoute) {
        this.drawDefaultPhaseIcon()
      }
      if (this.isVipRoute && this.isThirdSignal) {
        this.LanePhaseData = this.CrossDiagramMgr.compare(this.LanePhaseData, this.overlapLanePhaseData, 'type', 'nostatus')
        // console.log(this.LanePhaseData)
        this.sidewalkPhaseData = this.CrossDiagramMgr.compare(this.sidewalkPhaseData, this.overlapsidewalkPhaseData, 'pedtype', 'nostatus')
        let allDir = this.LanePhaseData.map(ele => ele.id)
        let allPedDir = this.sidewalkPhaseData.map(ele => ele.id)
        this.inneChoosedDirection = this.choosedDirection.filter(dir => allDir.indexOf(dir) !== -1)
        this.inneChoosedPedDirection = this.choosedPedDirection.filter(dir => allPedDir.indexOf(dir) !== -1)
        this.drawPhaseIcon()
      }
    },
    getBusPos () {
      // 公交相位信息
      this.busPhaseData = []
      this.crossInfo.phaseList.forEach((ele, i) => {
        if (ele.controltype >= 3 && ele.controltype <= 6) {
          ele.direction.forEach((dir, index) => {
          // 车道相位
            this.busPhaseData.push({
              key: this.CrossDiagramMgr.getUniqueKey('busphase'),
              phaseid: ele.id, // 相位id，用于对应相位状态
              id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
              name: this.PhaseDataModel.getBusPhasePos(dir).name,
              left: this.PhaseDataModel.getBusPhasePos(dir).x,
              top: this.PhaseDataModel.getBusPhasePos(dir).y,
              busleft: this.PhaseDataModel.getBusMapPos(dir).x,
              bustop: this.PhaseDataModel.getBusMapPos(dir).y,
              controltype: ele.controltype
            })
          })
        }
      })
      // 去掉重复方向的数据
      this.busPhaseData = Array.from(new Set(this.busPhaseData.map(item => item.id)))
        .map(id => this.busPhaseData.find(item => item.id === id))
    },
    createRandomType () {
      for (var i = 3; i <= 5; i++) {
        return Math.floor(Math.random() * (5 - 3)) + 3
      }
    },
    getPhasePos () {
      // 车道相位信息
      this.LanePhaseData = []
      this.crossInfo.phaseList.forEach((ele, i) => {
        if (ele.controltype === undefined || ele.controltype <= 2) {
          ele.direction.forEach((dir, index) => {
          // 车道相位
            this.LanePhaseData.push({
              key: this.CrossDiagramMgr.getUniqueKey('phase'),
              phaseid: ele.id, // 相位id，用于对应相位状态
              id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
              name: this.PhaseDataModel.getPhase(dir).name,
              left: this.PhaseDataModel.getPhase(dir).x,
              top: this.PhaseDataModel.getPhase(dir).y
            })
          })
        }
      })
      // 去掉重复方向的数据
      this.LanePhaseData = Array.from(new Set(this.LanePhaseData.map(item => item.id)))
        .map(id => this.LanePhaseData.find(item => item.id === id))
    },
    getOverlapPhasePos () {
      // 车道跟随相位信息
      if (!this.crossInfo.overlaplList) return
      this.overlapLanePhaseData = []
      this.crossInfo.overlaplList.forEach((ele, i) => {
        if (ele.direction) {
          ele.direction.forEach((dir, index) => {
            this.overlapLanePhaseData.push({
              key: this.CrossDiagramMgr.getUniqueKey('overlapphase'),
              phaseid: ele.id, // 相位id，用于对应相位状态
              id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
              name: this.PhaseDataModel.getPhase(dir).name,
              left: this.PhaseDataModel.getPhase(dir).x,
              top: this.PhaseDataModel.getPhase(dir).y
            })
          })
        }
      })
      // 去掉重复方向的数据
      this.overlapLanePhaseData = Array.from(new Set(this.overlapLanePhaseData.map(item => item.id)))
        .map(id => this.overlapLanePhaseData.find(item => item.id === id))
    },
    getRampPhasePos () {
      // 匝道车道相位信息
      this.LanePhaseData = []
      this.crossInfo.phaseList.forEach((ele, i) => {
        ele.direction.forEach((dir, index) => {
          if (ele.controltype === 0) {
            this.handleRampPhasePosData(`${i}-${index}`, ele, dir, this.PhaseDataModel.getMainPhasePos)
          }
          if (ele.controltype === 1) {
            this.handleRampPhasePosData(`${i}-${index}`, ele, dir, this.PhaseDataModel.getSidePhasePos)
          }
        })
      })
      // 去掉重复方向的数据
      this.LanePhaseData = Array.from(new Set(this.LanePhaseData.map(item => item.id)))
        .map(id => this.LanePhaseData.find(item => item.id === id))
    },
    handleRampPhasePosData (key, phase, dir) {
      let posInfo = phase.controltype === 0 ? this.PhaseDataModel.getMainPhasePos(dir) : this.PhaseDataModel.getSidePhasePos(dir)
      this.LanePhaseData.push({
        key,
        controlType: phase.controltype,
        phaseid: phase.id, // 相位id，用于对应相位状态
        id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
        name: posInfo.name,
        left: posInfo.x,
        top: posInfo.y
      })
    },
    getPedPhasePos () {
      // 行人相位信息
      this.sidewalkPhaseData = []
      this.crossInfo.phaseList.forEach((ele, i) => {
        if (ele.peddirection) {
          ele.peddirection.forEach((dir, index) => {
          // 行人相位
            if (this.PhaseDataModel.getSidePos(dir)) {
              let key = this.CrossDiagramMgr.getUniqueKey('pedphase')
              if (this.isVipRoute && this.isThirdSignal) {
                key = this.CrossDiagramMgr.getUniqueKey('pedphase') + `-${this.devId}`
              }
              this.sidewalkPhaseData.push({
                key,
                phaseid: ele.id, // 相位id，用于对应相位状态
                id: dir,
                name: this.PhaseDataModel.getSidePos(dir).name,
                left: this.PhaseDataModel.getSidePos(dir).x,
                top: this.PhaseDataModel.getSidePos(dir).y
              })
            }
          })
        }
      })
      // 去掉重复方向的数据
      this.sidewalkPhaseData = Array.from(new Set(this.sidewalkPhaseData.map(item => item.id)))
        .map(id => this.sidewalkPhaseData.find(item => item.id === id))
    },
    getOverlapPedPhasePos () {
      // 行人跟随相位信息
      if (!this.crossInfo.overlaplList) return
      this.overlapsidewalkPhaseData = []
      this.crossInfo.overlaplList.forEach((ele, i) => {
        if (ele.peddirection) {
          ele.peddirection.forEach((dir, index) => {
            if (this.PhaseDataModel.getSidePos(dir)) {
              this.overlapsidewalkPhaseData.push({
                key: this.CrossDiagramMgr.getUniqueKey('overlappedphase'),
                phaseid: ele.id, // 相位id，用于对应相位状态
                id: dir,
                name: this.PhaseDataModel.getSidePos(dir).name,
                left: this.PhaseDataModel.getSidePos(dir).x,
                top: this.PhaseDataModel.getSidePos(dir).y
              })
            }
          })
        }
      })
      // 去掉重复方向的数据
      this.overlapsidewalkPhaseData = Array.from(new Set(this.overlapsidewalkPhaseData.map(item => item.id)))
        .map(id => this.overlapsidewalkPhaseData.find(item => item.id === id))
    },
    getEffectDirectionPos(): void {
      if (!this.PhaseDataModel || !this.CrossDiagramMgr) return;
      this.effectDirData = getEffectDirectionPositions(
        this.effectiveDirection,
        this.PhaseDataModel,
        this.CrossDiagramMgr
      );
    },
    getCrossType(): void {
      // 使用工具函数获取路口类型
      this.crossType = getCrossType(this.mainType, this.mainDirection) as CrossType;
    },
    refresh () {
      this.getIntersectionInfo()
    },
    getpedStatus(): void {
      // 使用工具函数处理行人相位状态
      const curPedStatus = processPedStatus(
        this.sidewalkPhaseData,
        this.phaseStatusMap,
        this.SidewalkColorMap,
        'ped'
      );
      this.sidewalkPhaseData = JSON.parse(JSON.stringify(curPedStatus));
    },
    getOverlapPedStatus () {
      // 行人跟随相位状态
      let curPedStatus = []
      for (let i = 0; i < this.overlapsidewalkPhaseData.length; i++) {
        if (this.overlapsidewalkPhaseData[i].phaseid) {
          let curPhaseStatus = this.overlapPhaseStatusMap.get(this.overlapsidewalkPhaseData[i].phaseid)
          if (!curPhaseStatus) {
            // 无状态的行人道,也显示出来
            // const data = {
            //   ...this.overlapsidewalkPhaseData[i],
            //   pedtype: undefined
            // }
            // curPedStatus.push(data)
            continue
          }
          const data = {
            ...this.overlapsidewalkPhaseData[i],
            pedtype: curPhaseStatus.pedtype,
            color: this.SidewalkColorMap.get(curPhaseStatus.pedtype),
            flag: 'overlapped' // 行人跟随相位数据标识
          }
          curPedStatus.push(data)
        } else {
          // 无状态的行人道
          const data = {
            ...this.overlapsidewalkPhaseData[i],
            pedtype: undefined
          }
          curPedStatus.push(data)
        }
      }
      this.overlapsidewalkPhaseData = JSON.parse(JSON.stringify(curPedStatus))
    },
    getRoadDirection () {
      // 获取行车方向（从平台或配置工具的配置文件中读取）
      this.roadDir = this.roadDirection
    },
    getChannelInfo () {
      uploadSingleTscParam('channel', this.devId).then(data => {
        let res = data.data
        if (!res.success) {
          if (res.code === '4003') {
            // this.isShowMessage && this.$message.error(this.$t('platform.errorTip.devicenotonline'))
            if (this.isShowMessage) {
              console.log(this.$t('platform.errorTip.devicenotonline'))
            }
            return
          }
          // this.isShowMessage && this.$message.error(getMessageByCode(data.data.code, this.$i18n.locale))
          if (this.isShowMessage) {
            console.log(getMessageByCode(data.data.code, this.$i18n.locale))
          }
          return
        }
        let channelList = res.data.data.channelList.filter(ele => ele.type !== undefined)
        this.channelList = this.handleRepeatRealdir(channelList)
        console.log('this.channelList', this.channelList)
        this.handleChannelDirection()
      })
    },
    handleRepeatRealdir (channelList) {
      // 按realdir去掉重复方向的数据
      let dirChannelList = channelList.filter(ele => ele.realdir !== undefined && (ele.type === 0 || ele.type === 1 || ele.type === 3))
      let pedDirChannelList = channelList.filter(ele => ele.realdir !== undefined && ele.type === 2)
      let map = new Map()
      let map2 = new Map()
      dirChannelList.forEach(ele => {
        ele.realdir.forEach(dir => {
          if (map.get(dir) === undefined) {
            map.set(dir, ele)
          }
        })
      })
      pedDirChannelList.forEach(ele => {
        ele.realdir.forEach(dir => {
          if (map2.get(dir) === undefined) {
            map2.set(dir, ele)
          }
        })
      })
      let arr = Array.from(map)
      let pedarr = Array.from(map2)
      let newarr = []
      arr.forEach(ele => {
        ele[1].realdir = [ele[0]]
        let obj = {
          ...ele[1],
          realdir: [ele[0]]
        }
        newarr.push(obj)
      })
      pedarr.forEach(ele => {
        ele[1].realdir = [ele[0]]
        let obj = {
          ...ele[1],
          realdir: [ele[0]]
        }
        newarr.push(obj)
      })
      // console.log(newarr)
      return newarr
    },
    handleChannelDirection () {
      this.LanePhaseData = []
      this.sidewalkPhaseData = []
      this.sidewalkDir = []
      let realphasedirarr = []
      let realpeddirarr = []
      this.channelList.forEach((ele, i) => {
        if (ele.type === 0 || ele.type === 1 || ele.type === 3) {
          if (ele.realdir) {
            ele.realdir.forEach((dir, index) => {
            // 车道相位（通道类型是机动车，非机动车，公交时，对应相位机动车）
              this.LanePhaseData.push({
                key: this.CrossDiagramMgr.getUniqueKey('phase'),
                channelid: ele.id, // 通道id
                id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
                name: this.PhaseDataModel.getPhase(dir).name,
                left: this.PhaseDataModel.getPhase(dir).x,
                top: this.PhaseDataModel.getPhase(dir).y
              })
            })
            realphasedirarr = Array.from(new Set(realphasedirarr.concat(ele.realdir)))
          }
        }
        if (ele.type === 2) {
          if (ele.realdir) {
            ele.realdir.forEach((dir, index) => {
              // 行人相位
              if (this.sidewalkDir.indexOf(dir) === -1 && this.PhaseDataModel.getSidePos(dir)) {
                let obj = {
                  key: this.CrossDiagramMgr.getUniqueKey('pedphase') + `-${this.devId}`,
                  channelid: ele.id, // 通道id
                  id: dir,
                  name: this.PhaseDataModel.getSidePos(dir).name
                }
                if (this.channelType) {
                  obj.left = this.PhaseDataModel.getSidePos(dir).x
                  obj.top = this.PhaseDataModel.getSidePos(dir).y
                }
                this.sidewalkPhaseData.push(obj)
              }
            })
            realpeddirarr = Array.from(new Set(realpeddirarr.concat(ele.realdir)))
            this.sidewalkDir = Array.from(new Set([...this.sidewalkDir.concat(ele.realdir)]))
          }
        }
      })
      this.inneChoosedDirection = this.choosedDirection.filter(dir => realphasedirarr.indexOf(dir) !== -1)
      this.inneChoosedPedDirection = this.choosedPedDirection.filter(dir => realpeddirarr.indexOf(dir) !== -1)
      this.drawPhaseIcon()
    },
    async drawPhaseIcon () {
      const targetIds = [4, 8, 12, 16] // 掉头相位后画
      this.LanePhaseData = this.LanePhaseData.filter(item => !targetIds.includes(item.id)).concat(this.LanePhaseData.filter(item => targetIds.includes(item.id)))
      if (!this.isThirdSignal && this.isVipRoute) {
        await this.getConflictList()
        this.handleClickedPhase()
        this.compLanePhaseData = JSON.parse(JSON.stringify(this.LanePhaseData))
        this.handleClickedPedPhase()
        this.compSidewalkPhaseData = JSON.parse(JSON.stringify(this.sidewalkPhaseData))
      } else {
        // 第三方信号机不需要处理冲突关系
        this.handleClickedPhase()
        this.compLanePhaseData = JSON.parse(JSON.stringify(this.LanePhaseData))
        this.handleClickedPedPhase()
        this.compSidewalkPhaseData = JSON.parse(JSON.stringify(this.sidewalkPhaseData))
      }
    },
    handleClickedPhase () {
      if (!this.isThirdSignal) {
        this.inneChoosedDirection = this.inneChoosedDirection.filter(dir => this.phaseConflictList.indexOf(dir) === -1)
      }
      for (let index = 0; index < this.LanePhaseData.length; index++) {
        const element = this.LanePhaseData[index]
        if (this.inneChoosedDirection.indexOf(element.id) !== -1) {
          element.clicked = true
        }
      }
    },

    handleClickPhaseIcon (key, action) {
      let curClickedPhase = {}
      if (action === 'clicked') {
        for (let index = 0; index < this.LanePhaseData.length; index++) {
          const element = this.LanePhaseData[index]
          if (element.key === key) {
            element.clicked = true
            curClickedPhase = JSON.parse(JSON.stringify(element))
          }
        }
      }
      if (action === 'cancle') {
        for (let index = 0; index < this.LanePhaseData.length; index++) {
          const element = this.LanePhaseData[index]
          if (element.key === key) {
            delete element.clicked
          }
        }
      }
      this.compLanePhaseData = JSON.parse(JSON.stringify(this.LanePhaseData))
      let clickedDirection = this.compLanePhaseData.filter(ele => ele.clicked && !ele.disabled)
      this.inneChoosedDirection = clickedDirection.map(ele => ele.id)
      this.EmitAllChoosedDirection(curClickedPhase)
    },
    handleClickedPedPhase () {
      if (!this.isThirdSignal) {
        this.inneChoosedPedDirection = this.inneChoosedPedDirection.filter(dir => this.pedConflictList.indexOf(dir) === -1)
      }
      // 排他
      for (let index = 0; index < this.sidewalkPhaseData.length; index++) {
        const element = this.sidewalkPhaseData[index]
        delete element.clicked
      }
      for (let index = 0; index < this.sidewalkPhaseData.length; index++) {
        const element = this.sidewalkPhaseData[index]
        if (this.inneChoosedPedDirection.indexOf(element.id) !== -1) {
          element.clicked = true
        }
      }
    },
    handleClickSidewalkIcon (data, curChoosePed) {
      this.clickedPedDirection = data.filter(ele => ele.clicked && !ele.disabled)
      this.inneChoosedPedDirection = this.clickedPedDirection.map(ele => ele.id)
      this.EmitAllChoosedDirection(curChoosePed)
    },
    EmitAllChoosedDirection (curClickedPhase) {
      let allChoosedDir = {
        direction: this.inneChoosedDirection,
        peddirection: this.inneChoosedPedDirection
      }
      this.$emit('handleClickCrossIcon', allChoosedDir, curClickedPhase)
      this.drawPhaseIcon()
    },
    async getConflictList () {
      let ConflictList = new CrossDirectionConflictList(this.devId)
      return ConflictList.getConflictListByAgentid().then(res => {
        if (res) {
          let conflictList = ConflictList.getListDirConflict(this.inneChoosedDirection, this.inneChoosedPedDirection)
          this.phaseConflictList = conflictList.allConflictDir
          this.pedConflictList = conflictList.allPedConflictDir
          // 排他
          for (let index = 0; index < this.LanePhaseData.length; index++) {
            const element = this.LanePhaseData[index]
            delete element.disabled
          }
          for (let index = 0; index < this.sidewalkPhaseData.length; index++) {
            const element = this.sidewalkPhaseData[index]
            delete element.disabled
          }
          for (let index = 0; index < this.LanePhaseData.length; index++) {
            const element = this.LanePhaseData[index]
            if (this.phaseConflictList.indexOf(element.id) !== -1) {
              element.disabled = true
            }
          }
          for (let index = 0; index < this.sidewalkPhaseData.length; index++) {
            const element = this.sidewalkPhaseData[index]
            if (this.pedConflictList.indexOf(element.id) !== -1) {
              element.disabled = true
            }
          }
          // console.log(this.LanePhaseData)
        }
      })
    },
    createChannelStatusMap () {
      // 生成相位id和相位状态对应数据结构
      this.channelStatusList.map(channel => {
        let channelId = channel.id
        let channelInfo = {
          light: channel.light
        }
        this.channelStatusMap.set(channelId, channelInfo)
      })
    },
    getChannelPhaseStatus () {
      // 通道相位机动车状态
      this.comdirePhaseData = []
      let curLanePhaseData = []
      for (let i = 0; i < this.LanePhaseData.length; i++) {
        let curPhaseStatus = this.channelStatusMap.get(this.LanePhaseData[i].channelid)
        if (!curPhaseStatus) continue
        const data = {
          ...this.LanePhaseData[i],
          type: curPhaseStatus.light,
          color: this.ColorMap.get(curPhaseStatus.light),
          flag: 'phasechannel' // 车道相位数据标识
        }
        curLanePhaseData.push(data)
      }
      this.LanePhaseData = JSON.parse(JSON.stringify(curLanePhaseData))
      this.compLanePhaseData = JSON.parse(JSON.stringify(this.LanePhaseData))
    },
    getChannelPedStatus () {
      // 通道行人相位状态
      let curPedStatus = []
      for (let i = 0; i < this.sidewalkPhaseData.length; i++) {
        console.log(this.sidewalkPhaseData[i])
        if (this.sidewalkPhaseData[i].channelid) {
          let curPhaseStatus = this.channelStatusMap.get(this.sidewalkPhaseData[i].channelid)
          if (!curPhaseStatus) continue
          const data = {
            ...this.sidewalkPhaseData[i],
            pedtype: curPhaseStatus.light,
            color: this.ColorMap.get(curPhaseStatus.light),
            flag: 'pedchannel' // 行人相位数据标识
          }
          curPedStatus.push(data)
        } else {
          // 无状态的行人道
          const data = {
            ...this.sidewalkPhaseData[i],
            pedtype: undefined
          }
          curPedStatus.push(data)
        }
      }
      this.compSidewalkPhaseData = JSON.parse(JSON.stringify(curPedStatus))
    },
    handleValidDirections(): void {
      handleGetValidDirections(
        this.devId,
        (validDirections) => {
          this.effectiveDirection = validDirections;
          this.getEffectDirectionPos();
        },
        (errorMsg) => {
          console.log(errorMsg);
        }
      );
    },

    handleMergeDirections(): void {
      handleGetMergeDirections(
        this.devId,
        (mergeDirections) => {
          this.mergeDirection = mergeDirections;
          if (mergeDirections && mergeDirections.length > 0) {
            this.mergeShowList = mergeDirections.map(dir => ({
              id: dir,
              color: '#6e737c'
            }));
          } else {
            this.mergeShowList = [{
              id: '',
              color: '#fff'
            }];
          }
        },
        (errorMsg) => {
          console.log(errorMsg);
        }
      );
    },
    // 添加缺失的方法
    getRoadDirection(): void {
      // 获取行车方向（从平台或配置工具的配置文件中读取）
      this.roadDir = this.roadDirection;
    },

    refresh(): void {
      this.getIntersectionInfo();
    },

    // 添加事件处理方法
    handleClickPhaseIcon(data: any, action: string, key: string): void {
      // 处理相位图标点击事件
      console.log('handleClickPhaseIcon', data, action, key);
    },

    handleClickSidewalkIcon(data: any, curChoosePed: any): void {
      // 处理人行道图标点击事件
      console.log('handleClickSidewalkIcon', data, curChoosePed);
    }
  },
  mounted() {
    // this.init()
  }
});
</script>
<style scoped>
.invisible {
  visibility: hidden;
}
.crossImg{
    position: relative;
    width: 870px;
    height: 650px;
    left: 50%;
    transform: translateX(-50%);
}
.centerText {
  position: absolute;
  width: 140px;
  height: 140px;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  /* text-align: center; */
  z-index: 9;
  display: flex;
  align-items: center;
  /* padding-left: 16px; */
}
.phaseCountdown {
  line-height: 42PX;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #fff;
  width: 150px;
  margin: 0 auto;
}
.countdownBg {
  border-radius: 10PX;
  background-color: rgba(94, 90, 90, 0.8);
  padding-left: 14PX;
  padding-top: 10PX;
  padding-bottom: 10PX;
}
.centerText .text {
  display: inline-block;
  color: #299BCC;
  margin-top: 20PX;
}
.merge-direction-icon {
  width: 80PX;
  height: 80PX;
  margin: 0 auto;
}

.baseImg {
    width: 100%;
    height: 650px;
    position: relative;
}
</style>
