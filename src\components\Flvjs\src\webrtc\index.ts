
/* eslint-disable */
import type { PlayOptions, IWebrtcPlayer, WebRTCOptions } from '../types/index'

// 声明全局ZLMRTCClient类型
declare global {
  interface Window {
    ZLMRTCClient: any;
  }
  const ZLMRTCClient: any;
}

// 导出WebrtcPlayer类
export class WebrtcPlayer implements IWebrtcPlayer {
  // 初始化zml为null
  zml: any = null
  // 初始化_domId为空字符串
  _domId: string = ''
  // 选项参数
  options?: any

  // 构造函数，接收options参数
  constructor(options?: any) {
    this.options = options
  }

  play(domID: string, opt: PlayOptions): void {
    // 播放函数，接收domID和opt参数
    console.log('this.play opt:', opt)
    if (!opt.url) return
    this._domId = domID

    const videoDom = document.getElementById(this._domId) as HTMLVideoElement
    if (!videoDom) return

    const webrtcOptions: WebRTCOptions = {
      element: videoDom, // video 标签
      debug: false, // 是否打印日志
      zlmsdpUrl: opt.url, // 流地址
      simulcast: true,
      useCamera: true,
      audioEnable: false,
      videoEnable: true,
      recvOnly: true,
      resolution: {
        w: videoDom ? videoDom.offsetWidth : 0,
        h: videoDom ? videoDom.offsetHeight : 0
      },
      usedatachannel: false
    }

    this.zml = new ZLMRTCClient.Endpoint(webrtcOptions)
    if (!this.zml) return

    this.zml.on(ZLMRTCClient.Events.CAPTURE_STREAM_FAILED, () => {
      this.unPlay()
      console.log('this.play opt:', opt)
      this.play(this._domId, opt)
    })
  }

  unPlay(): void {
    const videoDom = document.getElementById(this._domId) as HTMLVideoElement
    try {
      if (videoDom) {
        videoDom.pause()
      }
    } catch (e) {
      console.error('Error pausing video:', e)
    }

    // 关闭流
    if (this.zml && this.zml.pc) {
      this.zml.pc.close()
    }
    this.zml = null
  }
}

// 替换为符合 ES Module 规范的导出方式
export default WebrtcPlayer
