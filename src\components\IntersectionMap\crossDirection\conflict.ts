import { ElMessage } from 'element-plus';
import { MessageData } from '@/api/business/signalManagement/types';
import { postDevsMessage } from '@/api/business/signalManagement';
import { getMessageByCode } from './utils';

// 类型定义
interface ChannelItem {
  id: number;
  realtype?: string;
  realdir?: number[];
  controltype?: number;
  controlsource?: number | number[];
}

interface PhaseItem {
  id: number;
  concurrent?: number[];
}

interface OverlapItem {
  id: number;
  includedphases?: number[];
}

interface ConflictInfo {
  channelid: number;
  greenconflict: number[];
}

interface UploadData {
  channelList: ChannelItem[];
  phaseList: PhaseItem[];
  overlaplList: OverlapItem[];
  channelGreenConflictInfo: ConflictInfo[];
  manualpanel?: any;
  channellock?: any[];
  detectorList?: any[];
  pedestrainDetectorList?: any[];
  preemptList?: any[];
  singleoptim?: any[];
}

interface ResultItem {
  direction: number;
  type: 'phase' | 'pedphase';
  laneConflictList: number[];
  pedConflictList: number[];
  conflictchannel?: number[];
}

interface TableDataItem {
  id: number;
  peddirection: number[];
  phasedirection: number[];
}

interface DefaultListItem {
  id: number;
  controlsource: number | number[];
  newCurren: number[];
  result?: number[];
  greenconflict?: number[];
}

interface ConflictResult {
  channelid: number;
  greenOther: number[];
  greenconflict: number[];
}

/**
 * 计算冲突关系
 * @param id 设备ID
 * @returns 冲突关系结果
 */
export async function computedRelation(id: string | number): Promise<ResultItem[] | undefined> {
  const uploadData = await UploadTscParam(id);
  if (!uploadData) return;

  const results: ResultItem[] = [];
  const channel = uploadData.channelList;

  const tableData: TableDataItem[] = channel.map(item => ({
    id: item.id,
    peddirection: item.realtype && item.realtype === 'peddirection' ? item.realdir || [] : [],
    phasedirection: item.realtype && item.realtype === 'direction' ? item.realdir || [] : []
  }));

  const colorArray = getDefault(uploadData);

  tableData.forEach(itemA => {
    (uploadData.channelGreenConflictInfo.length > 0 ? uploadData.channelGreenConflictInfo : colorArray).forEach(itemB => {
      if (itemA.id === itemB.channelid) {
        // 处理peddirection
        if (itemA.peddirection !== undefined) {
          itemA.peddirection.forEach(pedDir => {
            if (pedDir > 0) {
              results.push({
                direction: pedDir,
                type: 'pedphase',
                laneConflictList: [],
                conflictchannel: itemB.greenconflict,
                pedConflictList: []
              });
            }
          });
        }

        // 处理phasedirection
        if (itemA.phasedirection !== undefined) {
          itemA.phasedirection.forEach(phaseDir => {
            if (phaseDir > 0) {
              results.push({
                direction: phaseDir,
                type: 'phase',
                conflictchannel: itemB.greenconflict,
                laneConflictList: [],
                pedConflictList: []
              });
            }
          });
        }
      }
    });
  });

  results.forEach(item => {
    item.conflictchannel?.forEach(aaaValue => {
      channel.forEach(cha => {
        if (cha.id === aaaValue) {
          if (cha.realtype === 'direction') {
            if (cha.realdir) {
              item.laneConflictList.push(...cha.realdir);
            }
          } else if (cha.realtype === 'peddirection') {
            if (cha.realdir) {
              item.pedConflictList.push(...cha.realdir);
            }
          }
        }
      });
    });
    delete item.conflictchannel;
  });

  const uniqueDirectionsAndTypes = new Set<string>();
  const filteredResults = results.filter(obj => {
    const key = `${obj.direction}_${obj.type}`;
    if (uniqueDirectionsAndTypes.has(key)) {
      return false;
    }
    uniqueDirectionsAndTypes.add(key);
    return true;
  });

  const result = filteredResults.map(item => {
    const uniqueLaneConflictList = [...new Set(item.laneConflictList)];
    const uniquePedConflictList = [...new Set(item.pedConflictList)];
    return {
      ...item,
      laneConflictList: uniqueLaneConflictList,
      pedConflictList: uniquePedConflictList
    };
  });

  console.log('result', result);
  return result;
}

/**
 * 获取默认冲突配置
 * @param data 上传数据
 * @returns 默认冲突配置
 */
export function getDefault(data: UploadData): ConflictResult[] {
  const phaseList = data.phaseList;
  const overlaplList = data.overlaplList;
  const channel = data.channelList;

  const newPhase = phaseList.map(item => ({
    id: item.id,
    concurrent: item.concurrent ? item.concurrent : []
  }));

  const newOverlap = overlaplList.map(item => ({
    id: item.id,
    includedphases: item.includedphases || []
  }));

  const newChannel = channel.map(item => ({
    id: item.id,
    controltype: item.controltype,
    controlsource: item.controlsource
  }));

  const defaultList: DefaultListItem[] = newChannel.map(cha => {
    if (cha.controltype === 2 || cha.controltype === 3) {
      for (let i = 0; i < newPhase.length; i++) {
        if (cha.controlsource === newPhase[i].id) {
          return {
            id: cha.id,
            controlsource: cha.controlsource!,
            newCurren: newPhase[i].concurrent
          };
        }
      }
    } else if (cha.controltype === 4 || cha.controltype === 5) {
      const newLap = newOverlap.map(item => {
        const phaList: number[] = [];
        if (item.id === cha.controlsource) {
          phaList.push(...item.includedphases);
        }
        return {
          id: item.id,
          concurrent: Array.from(new Set(phaList))
        };
      });

      for (let i = 0; i < newLap.length; i++) {
        if (cha.controlsource === newLap[i].id) {
          return {
            id: cha.id,
            controlsource: newLap[i].concurrent,
            newCurren: newLap[i].concurrent
          };
        }
      }
    }

    return {
      id: cha.id,
      controlsource: cha.controlsource || 0,
      newCurren: []
    };
  }).filter(Boolean) as DefaultListItem[];

  const ret = defaultList.map((i, idx) => {
    const result: number[] = [];
    const greenconflict: number[] = [];

    defaultList.forEach((j, jdx) => {
      if (idx !== jdx) {
        const ic = Array.isArray(i.controlsource) ? i.controlsource : [i.controlsource];
        const jc = Array.isArray(j.controlsource) ? j.controlsource : [j.controlsource];
        let hasDuplicate = false;

        for (let i = 0; i < ic.length; i++) {
          for (let j = 0; j < jc.length; j++) {
            if (ic[i] === jc[j]) {
              hasDuplicate = true;
              break;
            }
          }
          if (hasDuplicate) {
            break;
          }
        }

        const isRelation = ic.some(x => jc.some(y => newPhase.find(m => m.id === x && m.concurrent.includes(y))));

        if (isRelation || hasDuplicate) {
          result.push(j.id);
        } else {
          greenconflict.push(j.id);
        }
      }
    });

    i.result = result;
    i.greenconflict = greenconflict;
    return i;
  });

  const newFlict: ConflictResult[] = ret.map(r => ({
    channelid: r.id,
    greenOther: r.result || [],
    greenconflict: r.greenconflict || []
  }));

  return newFlict;
}

/**
 * 上传TSC参数
 * @param id 设备ID
 * @returns 上传数据
 */
export function UploadTscParam(id: string | number): Promise<UploadData | undefined> {
  const messageData: MessageData = {
    devid: typeof id === 'string' ? parseInt(id) : id,
    operation: 'get-request',
    infotype: 'feature/all'
  };

  return postDevsMessage(messageData).then(data => {
    // 检查响应是否成功
    if (!data.data.success) {
      // 处理特定错误代码 4002 和子错误代码 4209
      if (data.data.code === '4002' && data.data.data?.errorCode === '4209') {
        const success = data.data.data.content?.success;
        if (success !== 0) {
          const errormsg = 'TSC控制错误: ' + success;
          ElMessage.error(errormsg);
          return undefined;
        }
      }

      // 处理错误应答 4002
      if (data.data.code === '4002') {
        // 子类型错误
        const childErrorCode = data.data.data?.errorCode;
        if (childErrorCode) {
          ElMessage.error(getMessageByCode(childErrorCode));
          return undefined;
        }
      }

      // 处理其他错误
      ElMessage.error(getMessageByCode(data.data.code));
      return undefined;
    }

    // 检查数据是否为空
    if (!data.data.data?.data || Object.keys(data.data.data.data).length === 0) {
      ElMessage.error('没有方案上传');
      return undefined;
    }

    // 解构数据，分离customInfo和其他TSC参数
    const { customInfo, ...allTscParam } = data.data.data.data;

    // 设置默认值，确保所有必需的属性都存在
    const processedParam: UploadData = {
      channelList: allTscParam.channelList || [],
      phaseList: allTscParam.phaseList || [],
      overlaplList: allTscParam.overlaplList || [],
      channelGreenConflictInfo: allTscParam.channelGreenConflictInfo || [],
      // 添加其他可能需要的属性
      manualpanel: allTscParam.manualpanel || {},
      channellock: allTscParam.channellock || [],
      detectorList: allTscParam.detectorList || [],
      pedestrainDetectorList: allTscParam.pedestrainDetectorList || [],
      preemptList: allTscParam.preemptList || [],
      singleoptim: allTscParam.singleoptim || []
    };

    return processedParam;
  }).catch(error => {
    ElMessage.error('上传TSC参数失败: ' + error.message);
    return undefined;
  });
}
