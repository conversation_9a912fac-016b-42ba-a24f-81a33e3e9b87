export default {
  // 路由国际化
  route: {
    dashboard: 'Dashboard',
    document: 'Document'
  },
  // 登录页面国际化
  login: {
    selectPlaceholder: 'Please select/enter a company name',
    username: 'Userna<PERSON>',
    password: 'Password',
    login: 'Login',
    logging: 'Logging...',
    code: 'Verification Code',
    rememberPassword: 'Remember me',
    switchRegisterPage: 'Sign up now',
    rule: {
      tenantId: {
        required: 'Please enter your tenant id'
      },
      username: {
        required: 'Please enter your account'
      },
      password: {
        required: 'Please enter your password'
      },
      code: {
        required: 'Please enter a verification code'
      }
    },
    social: {
      wechat: 'Wechat Login',
      maxkey: '<PERSON><PERSON><PERSON> Login',
      topiam: 'TopIam Login',
      gitee: 'Gitee Login',
      github: 'Github Login'
    }
  },
  // 注册页面国际化
  register: {
    selectPlaceholder: 'Please select/enter a company name',
    username: 'Userna<PERSON>',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    register: 'Register',
    registering: 'Registering...',
    registerSuccess: 'Congratulations, your {username} account has been registered!',
    code: 'Verification Code',
    switchLoginPage: 'Log in with an existing account',
    rule: {
      tenantId: {
        required: 'Please enter your tenant id'
      },
      username: {
        required: 'Please enter your account',
        length: 'The length of the user account must be between {min} and {max}'
      },
      password: {
        required: 'Please enter your password',
        length: 'The user password must be between {min} and {max} in length',
        pattern: "Can't contain illegal characters: {strings}"
      },
      code: {
        required: 'Please enter a verification code'
      },
      confirmPassword: {
        required: 'Please enter your password again',
        equalToPassword: 'The password entered twice is inconsistent'
      }
    }
  },
  // 导航栏国际化
  navbar: {
    full: 'Full Screen',
    language: 'Language',
    dashboard: 'Dashboard',
    document: 'Document',
    message: 'Message',
    layoutSize: 'Layout Size',
    selectTenant: 'Select Tenant',
    layoutSetting: 'Layout Setting',
    personalCenter: 'Personal Center',
    logout: 'Logout'
  },
  common: {
    yes: 'yes',
    no: 'no',
    enabled: 'enabled',
    disabled: 'disabled',
    add: 'add',
    edit: 'edit',
    delete: 'delete',
    clone: 'clone',
    view: 'view',
    export: 'export',
    import: 'import',
    preview: 'preview',
    download: 'download',
    upload: 'upload',
    search: 'search',
    reset: 'reset',
    save: 'save',
    cancel: 'cancel',
    confirm: 'confirm',
    submit: 'submit',
    close: 'close',
    operate: 'operate',
    status: 'status',
    remark: 'remark',
    sort: 'sort',
    action: 'action',
    select: 'select',
    input: 'input',
    required: 'required',
    alarm: 'Info',
    addfailed: 'Add Success!',
    addsucess: 'Add Failed!',
    addcancel: 'Add Canceled!',
    deletefailed: 'Delete Failed!',
    deletesucess: 'Delete Success!',
    deletecancel: 'Delete Canceled!',
    updatefailed: 'Update Failed!',
    updatesucess: 'Update Success!',
    updatecancel: 'Update Canceled!',
    relatefailed: 'Relate Failed!',
    relatesucess: 'Relate Success!',
    relatecancel: 'Relate Canceled!',
    savesucess: 'Save Success!',
  },
  platform:{
    'message': {
      // 全局异常错误
      '0001': 'Null pointer',
      '0002': 'Wrong request method',
      '0003': 'Incorrect input parameter type',
      '0004': 'Incomplete input parameters',
      '0005': 'Input parameters do not meet constraints',
      '0006': 'system error',
      '0007': 'Empty Name',
      '0008': 'Service Expired !',
      'errorcode': 'Error code',
      '10000': 'Parameter is empty',
      '10001': 'Parameter length error',
      '1002': 'Error',
      '10002': 'Record cannot be empty',
      '20003': 'id cannot be empty and cannot be 0',
      '20004': 'No parameter record',
      '20005': 'No device id found',
      '20006': 'Data stream close failed!',
      '20007': 'Json Type conversion failed!',
      '20008': 'File read failed!',
      '20009': 'File does not exist!',
      '20010': 'I/O close mistake!',
      // 用户管理错误
      '3001': 'Username does not exist',
      '3002': 'Authentication failed',
      '3003': 'Duplicate username',
      '3004': 'The old and new passwords cannot be the same',
      '3005': 'password can not be blank',
      '3006': 'The original password is wrong',
      '3007': 'Login user does not exist',
      '3008': 'No access',
      '3009': 'wrong password',
      '3010': 'Super user role cannot be modified',
      '3011': 'username or password is wrong',
      '3013': 'Account is disabled',
      '3014': 'token has expired',
      '3015': 'token failure',
      '3016': 'User organization is empty',
      '3017': 'Organization does not exist',
      '3018': 'Access ip is inconsistent with user ip, login is forbidden',
      '3019': 'the role can not be deleted beause it has related users',
      '3020': 'Failed to add role, role name already exists!',
      '3021': 'Failed to add super administrator role or user!',
      '3022': 'Failed to remove administrator role!',
      '3023': 'Administrator permissions cannot be modified！',
      '3024': 'The current user has not been created！',
      '3025': 'Token is not enabled！',
      '3026': 'The role has associated users!',
      '3027': 'The character does not exist!',
      // 消息通讯错误
      '4001': 'Error Request',
      '4002': 'Error Response',
      '4003': 'Device Not Online',
      '4004': 'Repeat ID',
      '4005': 'Device Empty Response',
      '4006': 'Device Response Operation Is None',
      '4007': 'Control Message Response Error',
      '4008': 'Device not online, ignored',
      '4009': 'Empty cache, please upload',
      '4010': 'Request Data Failed',
      '4011': 'Message types not supported',
      // 错误子类型
      // error-request
      '4100': 'agentid is null!',
      '4101': 'operation is null!',
      '4102': 'infotype is null!',
      '4103': 'infodata is null!',
      '4104': 'JSON format is incorrect!',
      '4105': 'agentid not exist!',
      '4106': 'Unknown operation type!',
      '4107': 'Unknown infotype!',
      '4108': 'protocol is null!',
      '4109': 'Receive Time Out or Receive Incorrect Data!',
      '4110': 'Last sendData is not finished!',
      '4111': 'Port is null!',
      '4112': 'Cache is null!',
      '4113': 'Keyints can not be null',
      '4114': 'Keyints can not be the first two when strategy is up!',
      '4115': 'Keyints can not be the last two when strategy is down!',
      '4116': 'No less than three intersections!',
      // error-response
      '4200': 'response is null!',
      '4201': 'response format error!',
      '4203': 'Unknown request instruction!',
      '4204': 'Comm Send Data error!',
      '4205': 'Udp Receive InfoType error by Send InfoType',
      '4206': 'Can not find UdpCommunication for Receive Msg',
      '4207': 'Error response from feature download',
      '4208': 'Error protocol data from respose message',
      '4209': 'Answer from atc',
      // device not online
      '4301': 'Device not online',
      // 协调路线错误
      '5001': 'Repeat Route Name',
      // 勤务路线错误
      '6001': 'Repeat Route Name',
      '6002': 'The route is on execution',
      '6003': 'VipRoute Cannot cancel! Control is changed.',
      // 流量错误
      '7001': 'ftp clent connect fail',
      // 组织机构错误
      '11001': 'Repeat Name!',
      // 路口管理错误
      '8001': 'device not found By Agentid!',
      '8002': 'device is found By CoordinateRoute',
      '8003': 'device is found By VipRoute',
      '8004': 'Agentid is duplicated',
      // 错误类型子类型，返回码第二层
      '8101': 'Device can not delete!',
      // 瓶颈控制错误
      '9001': 'Overflow control failed!',
      '9002': 'Overflow recovery failed!',
      '9003': 'Overflow is executing, can not be deleted!',
      '9004': 'Overflow pattern name is already exist!',
      '9005': 'Overflow pattern is empty!',
      '9006': 'No Overflow pattern is in execution!'
    }
  }
};
