<template>
  <el-dialog
    v-model="visible"
    title="方案优化"
    width="80%"
    @close="handleClose"
  >
    <div v-if="pattern" class="optimize-content">
      <!-- <PatternOptimize
        :pattern="pattern"
        @optimize-success="handleOptimizeSuccess"
      /> -->
    </div>
    
    <template #footer>
      <el-button @click="handleClose">
        取消
      </el-button>
      <el-button type="primary" @click="handleOptimize">
        优化
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
// import PatternOptimize from './PatternOptimize.vue'
import type { Pattern } from '../types'

interface Props {
  modelValue: boolean
  pattern: Pattern | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'optimize'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleClose = () => {
  visible.value = false
}

const handleOptimize = () => {
  emit('optimize')
}

const handleOptimizeSuccess = () => {
  handleClose()
}
</script>

<style scoped>
.optimize-content {
  min-height: 400px;
}
</style>

