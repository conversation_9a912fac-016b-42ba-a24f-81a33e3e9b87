<template>
  <!-- 单视频播放组件 -->
  <div class="flvplayer" v-if="curVideoInfo">
    <div class="flv-header">
      <span class="title">{{ Title || title }}</span>
      <span class="list-icon">
        <div>
          <i
            class="iconfont icon-shipinshuaxin"
            ref="videoRefresh"
            @click="reload(curVideoInfo.flvPlayer)"
          ></i>
        </div>
        <slot name="vediolist"></slot>
      </span>
    </div>
    <div class="flv-content">
      <video
        v-if="!useWebRTC"
        class="flv-video"
        autoplay
        controls
        :width="Width"
        :height="Height"
        :id="`videoElement${curVideoInfo.id}`"
      ></video>
      <video
        v-else
        :id="`cameravideo${curVideoInfo.id}`"
        class="widget-player-vide webrtc-video"
        controls
        autoplay
        controlsList="nodownload"
        disablePictureInPicture
        style="text-align: left; width: 100%; height: 100%; object-fit: fill"
        @pause="handlePause"
      >
        Your browser is too old which doesn't support HTML5 video.
      </video>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import flvjs from 'flv.js'
import { usePlayer } from './hook/index'
import type { VideoInfo, FlvPlayerProps, FlvPlayerEmits, FlvPlayerConfig, FlvPlayerOptions, PlayOptions } from './types/index'

// 定义props
interface Props extends FlvPlayerProps {}

const props = withDefaults(defineProps<Props>(), {
  Width: '100%',
  Height: '237',
  autoPlay: false
})

// 定义emits
const emit = defineEmits<FlvPlayerEmits>()

// 响应式数据
const playPromise = ref<Promise<void> | null>(null)
const timer = ref<number>(0)
const reloadTimer = ref<number>(0)
const checkPausTimer = ref<number>(0)
const title = ref<string>(props.Title || '')
const useWebRTC = ref<boolean>(true) // 是否优先使用 WebRTC
const webRTCPeerConnection = ref<any>(null) // WebRTC 对象
const player = ref<any>(null)
const videoRefresh = ref<HTMLElement>()

// 获取播放器实例
const { webrtcPlayer, replay } = usePlayer()
let webrtcPlayerInstance = webrtcPlayer
// 组件挂载
onMounted(() => {
  if (!props.curVideoInfo || Object.keys(props.curVideoInfo).length === 0) return
  if (props.curVideoInfo.hasOwnProperty('name')) {
    title.value = props.curVideoInfo.name
  }
  nextTick(() => {
    createVideo()
    if (!useWebRTC.value) {
      reloadTimer.value = window.setTimeout(() => {
        // 检查 player 是否存在
        if (player.value) {
          player.value.on(flvjs.Events.ERROR, (errorType: any, errorDetail: any, errorInfo: any) => {
            console.log('errorType:', errorType)
            console.log('errorDetail:', errorDetail)
            console.log('errorInfo:', errorInfo)
            // 视频出错后销毁重新创建
            const dom = videoRefresh.value
            dom && dom.click()
          })

          player.value.on('statistics_info', function (res: any) {
            console.log('statistics_info:', res)
            const dom = videoRefresh.value
            dom && dom.click()
          })
        }

        if (checkPausTimer.value) {
          clearInterval(checkPausTimer.value)
        }
        checkPausTimer.value = window.setInterval(() => {
          if (player.value) {
            const buffered = player.value.buffered
            const currentTime = player.value.currentTime
            if (buffered.length > 0) {
              const bufferEnd = buffered.end(buffered.length - 1)
              const bufferLength = bufferEnd - currentTime
              console.log(`Buffer length: ${bufferLength.toFixed(2)}s`)
              if (bufferLength < 0.1) { // 缓冲不足阈值
                console.warn('低缓冲警告！可能出现卡顿: ', title.value)
                console.log('reload player.>>>')
                const dom = videoRefresh.value
                dom && dom.click()
              }
            }
          }
        }, 3 * 1000)
      }, 5 * 1000)
    }
  })
})

// 监听curVideoInfo变化
watch(
  () => props.curVideoInfo,
  (val: VideoInfo | undefined) => {
    if (!val) return
    if (val.hasOwnProperty('name')) {
      title.value = val.name
    }
    // checkurl
    if (val.url) {
      useWebRTC.value = val.url.indexOf('.flv') === -1
    }
  },
  { deep: true }
)
// 创建视频
const createVideo = () => {
  if (useWebRTC.value) {
    createWebRTCVideo()
  } else if (flvjs.isSupported()) {
    createFLVVideo()
  }
}

// 检查播放选项
const checkOpt = (): PlayOptions => {
  const playOpt: PlayOptions = { type: 1, url: '' }
  if (props.curVideoInfo) {
    playOpt.url = props.curVideoInfo.url
  }
  const curOpt = JSON.parse(JSON.stringify(playOpt)) as PlayOptions
  if (curOpt.type === 0 && curOpt.activeSei && props.curVideoInfo) {
    curOpt.seiCanvasContainer = `sei-canvas-${props.curVideoInfo.url}`
  }
  return curOpt
}

// 创建WebRTC视频
const createWebRTCVideo = () => {
  if (!props.curVideoInfo || !props.curVideoInfo.url) {
    console.error('curVideoInfo or url is not provided')
    return
  }

  const timestamp = new Date().getTime()
  const timeParamsStr = `&st=${timestamp}`
  // http://10.165.33.195:30555/api/video/index/api/webrtc?app=live&stream=znlptz&type=play
  const videoInfo = props.curVideoInfo as VideoInfo
  videoInfo.url = videoInfo.url + timeParamsStr

  videoInfo.videoElement = document.getElementById(`cameravideo${videoInfo.id}`) as HTMLVideoElement
  nextTick(() => {
    if (videoInfo) {
      replay(`cameravideo${videoInfo.id}`, checkOpt())
    }
  })
}
// 创建FLV视频
const createFLVVideo = () => {
  if (flvjs.isSupported()) {
    if (!props.curVideoInfo) return

    const videoInfo = props.curVideoInfo as VideoInfo
    videoInfo.videoElement = document.getElementById(`videoElement${videoInfo.id}`) as HTMLVideoElement

    const flvPlayerConfig: FlvPlayerConfig = {
      type: 'flv',
      isLive: true,
      url: videoInfo.url
    }

    const flvPlayerOptions: FlvPlayerOptions = {
      stashInitialSize: 128
    }

    const flvPlayer = flvjs.createPlayer(flvPlayerConfig, flvPlayerOptions)
    player.value = flvPlayer
    videoInfo.flvPlayer = flvPlayer
    flvPlayer.attachMediaElement(videoInfo.videoElement)

    timer.value = window.setTimeout(() => {
      flvPlayer.load()
      if (props.autoPlay) {
        flvPlayer.play() // 播放
      } else {
        flvPlayer.pause()
      }
    }, 1 * 1000)
  }
}

// 处理暂停事件
const handlePause = (e: Event) => {
  if (!e.target) return
  const video = e.target as HTMLVideoElement
  video.play()
}

// 播放媒体
const play = (media: any) => {
  media.play()
}
// 重新加载
const reload = (media?: any) => {
  console.log('reload media:', props.curVideoInfo)
  // 重新加载监控视频（以当前时间为准）
  if (useWebRTC.value) {
    webrtc_destroy()
    createWebRTCVideo()
  } else {
    flv_destroy(media)
    createFLVVideo()
  }
}

// FLV暂停
const flv_pause = (media: any) => {
  media.pause()
}

// FLV销毁
const flv_destroy = (media: any) => {
  if (media) {
    media.unload()
    media.detachMediaElement()
    media.destroy()
    media = null
  }
}

// WebRTC销毁
const webrtc_destroy = () => {
  if (webrtcPlayerInstance) {
    webrtcPlayerInstance.unPlay()
    webrtcPlayerInstance = null
  }
}

// 组件卸载前清理
onBeforeUnmount(() => {
  clearTimeout(timer.value)
  clearTimeout(reloadTimer.value)
  clearTimeout(checkPausTimer.value)
  if (useWebRTC.value) {
    webrtc_destroy()
  } else if (props.curVideoInfo) {
    flv_destroy(props.curVideoInfo.flvPlayer)
  }
})
</script>

<style lang="scss" scoped>
@import './styles/index.scss';
</style>
