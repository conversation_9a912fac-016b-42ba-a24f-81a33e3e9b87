<!--方向列表配置组件-->
<template>
  <div class="direction-list-configuration">
    <div class="dir-config-row">
      <div class="label" :style="{ width: labelWidth }">车道方向：</div>
      <div class="dir-btn lane-dir">
        <div
          class="each-icon"
          v-for="(item, index) in laneList"
          :key="index"
          :class="item.disabled ? 'disabled-icon' : ''"
        >
          <el-tooltip class="item" effect="dark" :content="item.name" placement="bottom-start">
            <div
              class="single-icon"
              @click="selectDire(item, 'lane')"
              :class="!item.disabled && preselectDirection.indexOf(item.id) !== -1 ? 'single-icon-select' : ''"
            >
              <i :class="item.iconclass"></i>
              <div class="single-icon-name">{{ item.name }}</div>
            </div>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="dir-config-row">
      <div class="label" :style="{ width: labelWidth }">行人方向：</div>
      <div class="dir-btn ped-dir">
        <div
          class="each-icon"
          v-for="(item, index) in pedList"
          :key="index"
          :class="item.disabled ? 'disabled-icon' : ''"
        >
          <el-tooltip class="item" effect="dark" :content="item.name" placement="bottom-start">
            <div
              class="single-icon ped-icon"
              @click="selectDire(item, 'ped')"
              :class="!item.disabled && preselectPedDirection.indexOf(item.id) !== -1 ? 'single-icon-select' : ''"
            >
              <PedSvg :pedId="item.id" />
              <div class="single-icon-name" style="margin-top: 5px;">{{ item.name }}</div>
            </div>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, type PropType } from 'vue';
import { ElMessage } from 'element-plus';
import PedSvg from './svg/pedSvg.vue';
import { getMessageByCode } from '@/components/IntersectionMap/crossDirection/utils';
import { postDevsMessage } from '@/api/business/signalManagement';
import CrossDiagramMgr from '@/components/EdgeMgr/controller/crossDiagramMgr';
import PhaseDataModel from '@/components/PhaseDataModel';
import { getPhaseIcons } from '@/utils/phasedesc';
import CrossDirectionConflictList from '@/components/IntersectionMap/crossDirection/conflictList';
import type {
  PhaseDataItem,
  PedPhaseDataItem,
  ChannelDataItem,
  IntersectionInfo,
  PhaseListItem,
  OverlapListItem,
  SelectDirectionData
} from './types';
import type { RoadDirection } from '@/components/IntersectionMap/crossDirection/types';

export default defineComponent({
  name: 'DirectionListConfiguration',
  components: {
    PedSvg
  },
  props: {
    labelWidth: {
      type: String,
      default: '130px'
    },
    agentId: {
      type: String
    },
    list: {
      type: Array,
      default: () => []
    },
    roadDirection: {
      type: String as PropType<RoadDirection>,
      default: 'right'
    },
    isThirdSignal: {
      type: Boolean,
      default: false
    },
    choosedDirection: {
      type: Array as PropType<number[]>,
      default: () => []
    },
    choosedPedDirection: {
      type: Array as PropType<number[]>,
      default: () => []
    }
  },
  emits: ['handleClickCrossIcon'],
  data() {
    return {
      laneList: [] as PhaseDataItem[],
      pedList: [] as PedPhaseDataItem[],
      preselectDirection: [] as number[],
      preselectPedDirection: [] as number[],

      // 内部数据
      CrossDiagramMgrInstance: null as any,
      PhaseDataModelInstance: null as any,
      tempType: '' as string,
      mainType: '' as string,
      mainDirection: '' as string,
      crossInfo: null as any,

      LanePhaseData: [] as PhaseDataItem[],
      overlapLanePhaseData: [] as PhaseDataItem[],
      sidewalkPhaseData: [] as PedPhaseDataItem[],
      overlapsidewalkPhaseData: [] as PedPhaseDataItem[],
      compLanePhaseData: [] as PhaseDataItem[],
      compSidewalkPhaseData: [] as PedPhaseDataItem[],
      inneChoosedDirection: [] as number[],
      inneChoosedPedDirection: [] as number[],
      channelList: [] as ChannelDataItem[],
      sidewalkDir: [] as number[],
      phaseConflictList: [] as number[],
      pedConflictList: [] as number[],
      choosedLanePhase: [] as PhaseDataItem[],
      choosedPedPhase: [] as PedPhaseDataItem[]
    };
  },
  methods: {
    // 初始化方法
    init(IntersectionInfo: IntersectionInfo): void {
      this.CrossDiagramMgrInstance = new CrossDiagramMgr();
      this.PhaseDataModelInstance = new PhaseDataModel(this.roadDirection);
      this.getIntersectionInfo(IntersectionInfo);
    },

    // 获取路口信息
    getIntersectionInfo(res: IntersectionInfo): void {
      this.tempType = res.data.data.type;
      this.mainType = this.tempType.split('-')[0];
      this.mainDirection = this.tempType.split('-')[1];

      if (!this.isThirdSignal) {
        // 可点击模式下，非第三方设备，按通道显示相位方向
        this.getChannelInfo();
        return;
      }

      this.crossInfo = res.data.data.param;
      // 城市道路加载车道相位坐标和人行道坐标
      this.getPhasePos();
      this.getOverlapPhasePos();
      this.getPedPhasePos();
      this.getOverlapPedPhasePos();

      if (this.isThirdSignal) {
        // 第三方设备，按相位方向显示相位方向
        this.compLanePhaseData = this.CrossDiagramMgrInstance.compare(
          this.LanePhaseData,
          this.overlapLanePhaseData,
          'type',
          'nostatus'
        );
        this.compSidewalkPhaseData = this.CrossDiagramMgrInstance.compare(
          this.sidewalkPhaseData,
          this.overlapsidewalkPhaseData,
          'pedtype',
          'nostatus'
        );

        const allDir = this.compLanePhaseData.map((ele: any) => ele.id);
        const allPedDir = this.compSidewalkPhaseData.map((ele: any) => ele.id);
        this.inneChoosedDirection = (this.choosedDirection || []).filter((dir: number) => allDir.indexOf(dir) !== -1);
        this.inneChoosedPedDirection = (this.choosedPedDirection || []).filter((dir: number) => allPedDir.indexOf(dir) !== -1);
        this.drawPhaseIcon();
      }
    },

    // 获取车道相位信息
    getPhasePos(): void {
      this.LanePhaseData = [];
      if (!this.crossInfo || !this.crossInfo.phaseList) return;

      this.crossInfo.phaseList.forEach((ele: PhaseListItem) => {
        if (ele.controltype === undefined || ele.controltype <= 2) {
          if (ele.direction && Array.isArray(ele.direction)) {
            ele.direction.forEach((dir: number) => {
              const dirinfo = this.PhaseDataModelInstance.getPhase(dir);
              if (dir <= 16 && dirinfo) {
                this.LanePhaseData.push({
                  key: this.CrossDiagramMgrInstance.getUniqueKey('phase'),
                  phaseid: ele.id,
                  id: dir,
                  name: dirinfo.name || `方向${dir}`
                });
              }
            });
          }
        }
      });
    },

    // 获取跟随相位信息
    getOverlapPhasePos(): void {
      if (!this.crossInfo || !this.crossInfo.overlaplList) return;
      this.overlapLanePhaseData = [];
      this.crossInfo.overlaplList.forEach((ele: OverlapListItem) => {
        if (ele.direction && Array.isArray(ele.direction)) {
          ele.direction.forEach((dir: number) => {
            const phaseInfo = this.PhaseDataModelInstance.getPhase(dir);
            if (phaseInfo) {
              this.overlapLanePhaseData.push({
                key: this.CrossDiagramMgrInstance.getUniqueKey('overlapphase'),
                phaseid: ele.id,
                id: dir,
                name: phaseInfo.name || `方向${dir}`,
                left: phaseInfo.x,
                top: phaseInfo.y
              });
            }
          });
        }
      });
    },
    getPedPhasePos(): void {
      this.sidewalkPhaseData = [];
      if (!this.crossInfo || !this.crossInfo.phaseList) return;

      this.crossInfo.phaseList.forEach((ele: PhaseListItem) => {
        if (ele.peddirection && Array.isArray(ele.peddirection)) {
          ele.peddirection.forEach((dir: number) => {
            const dirinfo = this.PhaseDataModelInstance.getSidePos(dir);
            if (dir <= 16 && dirinfo) {
              const key = this.CrossDiagramMgrInstance.getUniqueKey('pedphase');
              this.sidewalkPhaseData.push({
                key,
                phaseid: ele.id,
                id: dir,
                name: dirinfo.name || dirinfo.desc || `方向${dir}`
              });
            }
          });
        }
      });
    },

    // 获取行人跟随相位信息
    getOverlapPedPhasePos(): void {
      if (!this.crossInfo || !this.crossInfo.overlaplList) return;
      this.overlapsidewalkPhaseData = [];
      this.crossInfo.overlaplList.forEach((ele: OverlapListItem) => {
        if (ele.peddirection && Array.isArray(ele.peddirection)) {
          ele.peddirection.forEach((dir: number) => {
            const sidePos = this.PhaseDataModelInstance.getSidePos(dir);
            if (sidePos) {
              this.overlapsidewalkPhaseData.push({
                key: this.CrossDiagramMgrInstance.getUniqueKey('overlappedphase'),
                phaseid: ele.id,
                id: dir,
                name: sidePos.name || sidePos.desc || `方向${dir}`,
                left: sidePos.x,
                top: sidePos.y
              });
            }
          });
        }
      });
    },

    // 获取通道信息
    async getChannelInfo(): Promise<void> {
      try {
        const messageData = {
          devid: parseInt(this.agentId || '0'),
          operation: 'get',
          infotype: 'channel',
          data: {}
        };
        const data = await postDevsMessage(messageData);
        const res = data.data;
        if (!res.success) {
          if (res.code === '4003') {
            ElMessage.error('设备未在线');
            return;
          }
          ElMessage.error(getMessageByCode(res.code));
          return;
        }
        // 修复数据结构访问
        const channelData = res.data || res;
        const channelList = channelData.channelList || channelData.data?.channelList || [];
        const filteredChannelList = channelList.filter((ele: ChannelDataItem) => ele.type !== undefined);
        this.channelList = this.handleRepeatRealdir(filteredChannelList);
        this.handleChannelDirection();
      } catch (error) {
        console.error('获取通道信息失败:', error);
      }
    },

    // 处理重复方向
    handleRepeatRealdir(channelList: ChannelDataItem[]): ChannelDataItem[] {
      const map = new Map();
      channelList.forEach(ele => {
        if (ele.realdir !== undefined && ele.realdir.length > 0) {
          ele.realdir.forEach(dir => {
            if (map.get(dir) === undefined) {
              map.set(dir, ele);
            }
          });
        }
      });
      const arr = Array.from(map);
      const newarr: ChannelDataItem[] = [];
      arr.forEach(ele => {
        newarr.push(ele[1] as ChannelDataItem);
      });
      return newarr;
    },
    // 处理通道方向
    handleChannelDirection(): void {
      this.LanePhaseData = [];
      this.sidewalkPhaseData = [];
      this.sidewalkDir = [];
      let realphasedirarr: number[] = [];
      let realpeddirarr: number[] = [];

      this.channelList.forEach((ele: ChannelDataItem) => {
        if (ele.type === 0 || ele.type === 1 || ele.type === 3) {
          if (ele.realdir) {
            ele.realdir.forEach((dir: number) => {
              const phaseinfo = this.PhaseDataModelInstance.getPhase(dir);
              this.LanePhaseData.push({
                key: this.CrossDiagramMgrInstance.getUniqueKey('phase'),
                channelid: ele.id,
                id: dir,
                name: phaseinfo.name
              });
            });
            realphasedirarr = Array.from(new Set(realphasedirarr.concat(ele.realdir)));
          }
        }
        if (ele.type === 2) {
          if (ele.realdir) {
            ele.realdir.forEach((dir: number) => {
              const pedinfo = this.PhaseDataModelInstance.getSidePos(dir);
              if (this.sidewalkDir.indexOf(dir) === -1 && pedinfo) {
                this.sidewalkPhaseData.push({
                  key: this.CrossDiagramMgrInstance.getUniqueKey('pedphase'),
                  channelid: ele.id,
                  id: dir,
                  name: pedinfo.desc || pedinfo.name || `方向${dir}`
                });
              }
            });
            realpeddirarr = Array.from(new Set(realpeddirarr.concat(ele.realdir)));
            this.sidewalkDir = Array.from(new Set([...this.sidewalkDir.concat(ele.realdir)]));
          }
        }
      });

      this.inneChoosedDirection = (this.choosedDirection || []).filter((dir: number) => realphasedirarr.indexOf(dir) !== -1);
      this.inneChoosedPedDirection = (this.choosedPedDirection || []).filter((dir: number) => realpeddirarr.indexOf(dir) !== -1);
      this.drawPhaseIcon();
    },

    // 绘制相位图标
    async drawPhaseIcon(): Promise<void> {
      if (!this.isThirdSignal) {
        await this.getConflictList();
        this.handleClickedPhase();
        this.handleClickedPedPhase();
        this.handleLaneDir();
        this.handlePedDir();
      } else {
        this.handleClickedPhase();
        this.handleClickedPedPhase();
        this.handleLaneDir();
        this.handlePedDir();
      }
    },

    // 获取冲突列表
    async getConflictList(): Promise<void> {
      try {
        const ConflictList = new CrossDirectionConflictList(this.agentId || '');
        const res = await ConflictList.getConflictListByAgentid();
        if (res) {
          const conflictList = ConflictList.getListDirConflict(this.inneChoosedDirection, this.inneChoosedPedDirection);
          this.phaseConflictList = conflictList.allConflictDir;
          this.pedConflictList = conflictList.allPedConflictDir;

          // 排他 - 重置禁用状态
          this.LanePhaseData.forEach(element => {
            delete element.disabled;
          });
          this.sidewalkPhaseData.forEach(element => {
            delete element.disabled;
          });

          // 设置冲突禁用状态
          this.LanePhaseData.forEach(element => {
            if (this.phaseConflictList.indexOf(element.id) !== -1) {
              element.disabled = true;
            }
          });
          this.sidewalkPhaseData.forEach(element => {
            if (this.pedConflictList.indexOf(element.id) !== -1) {
              element.disabled = true;
            }
          });
        }
      } catch (error) {
        console.error('获取冲突列表失败:', error);
      }
    },
    // 处理已点击的相位
    handleClickedPhase(): void {
      if (!this.isThirdSignal) {
        this.preselectDirection = this.inneChoosedDirection.filter((dir: number) => this.phaseConflictList.indexOf(dir) === -1);
      } else {
        this.preselectDirection = [...this.inneChoosedDirection];
      }
    },

    // 处理已点击的行人相位
    handleClickedPedPhase(): void {
      if (!this.isThirdSignal) {
        this.preselectPedDirection = this.inneChoosedPedDirection.filter((dir: number) => this.pedConflictList.indexOf(dir) === -1);
      } else {
        this.preselectPedDirection = [...this.inneChoosedPedDirection];
      }
    },

    // 处理车道方向
    handleLaneDir(): void {
      if (!this.isThirdSignal) {
        this.laneList = this.getPhaseDirIcon(this.LanePhaseData);
      } else {
        this.laneList = this.getPhaseDirIcon(this.compLanePhaseData);
      }
    },

    // 处理行人方向
    handlePedDir(): void {
      if (!this.isThirdSignal) {
        this.pedList = [...this.sidewalkPhaseData];
      } else {
        this.pedList = [...this.compSidewalkPhaseData];
      }
    },

    // 获取相位方向图标
    getPhaseDirIcon(list: PhaseDataItem[]): PhaseDataItem[] {
      const idarr: number[] = [];
      const dirlist: PhaseDataItem[] = [];

      try {
        const images = getPhaseIcons(this.roadDirection);

        for (let i = 0; i < list.length; i++) {
          if (list[i].id <= 16 && idarr.indexOf(list[i].id) === -1) {
            idarr.push(list[i].id);
            const iconItem = images.find((ele: any) => ele.id === list[i].id);
            let obj: PhaseDataItem = {
              ...list[i],
              iconclass: iconItem?.class || `phase-icon-${list[i].id}`
            };

            dirlist.push(obj);
          }
        }
      } catch (error) {
        console.error('获取相位图标失败:', error);
        // 如果获取图标失败，返回基本的列表
        return list.map(item => ({
          ...item,
          iconclass: `phase-icon-${item.id}`
        }));
      }

      return dirlist;
    },

    // 选择方向
    selectDire(value: PhaseDataItem | PedPhaseDataItem, type: 'lane' | 'ped'): void {
      if (value.disabled) return;

      const id = value.id;
      if (type === 'lane') {
        const index = this.preselectDirection.indexOf(id);
        if (index === -1) {
          this.preselectDirection.push(id);
        } else {
          this.preselectDirection.splice(index, 1);
        }
        this.choosedLanePhase = this.laneList.filter((lane: PhaseDataItem) => this.preselectDirection.includes(lane.id));
        this.inneChoosedDirection = [...this.preselectDirection];
      }

      if (type === 'ped') {
        const index = this.preselectPedDirection.indexOf(id);
        if (index === -1) {
          this.preselectPedDirection.push(id);
        } else {
          this.preselectPedDirection.splice(index, 1);
        }
        this.choosedPedPhase = this.pedList.filter((lane: PedPhaseDataItem) => this.preselectPedDirection.includes(lane.id));
        this.inneChoosedPedDirection = [...this.preselectPedDirection];
      }

      this.EmitAllChoosedDirection(value);
    },

    // 发送所有选择的方向
    EmitAllChoosedDirection(curClickedPhase: PhaseDataItem | PedPhaseDataItem): void {
      const allChoosedDir: SelectDirectionData = {
        direction: [...this.preselectDirection],
        peddirection: [...this.preselectPedDirection]
      };
      this.$emit('handleClickCrossIcon', allChoosedDir, curClickedPhase);
      // 避免无限循环，只在非第三方信号机时重新绘制
      if (!this.isThirdSignal) {
        this.drawPhaseIcon();
      }
    }
  },
  mounted () {
    // this.init()
  }
});
</script>

<style lang="scss" scoped>
.direction-list-configuration {
  .dir-config-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .label {
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }

    .dir-btn {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .each-icon {
        position: relative;

        &.disabled-icon {
          opacity: 0.5;
          cursor: not-allowed;

          .single-icon {
            cursor: not-allowed;
          }
        }

        .single-icon {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 60px;
          height: 60px;
          border: 2px solid #ddd;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            border-color: #409eff;
          }

          &.single-icon-select {
            border-color: #409eff;
            background-color: #ecf5ff;
          }

          i {
            font-size: 24px;
            margin-bottom: 4px;
          }

          .single-icon-name {
            font-size: 12px;
            text-align: center;
            line-height: 1.2;
          }

          &.ped-icon {
            .single-icon-name {
              margin-top: 5px;
            }
          }
        }
      }
    }
  }
}
</style>
