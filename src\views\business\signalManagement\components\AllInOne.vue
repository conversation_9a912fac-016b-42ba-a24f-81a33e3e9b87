<template>
    <el-tabs :tab-position="tabPosition" class="demo-tabs" v-model="activeTab">
        <el-tab-pane label="相位" name="phase">
            <keep-alive>
                <Phase></Phase>
            </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="方案" name="pattern">
            <keep-alive>
                <Pattern></Pattern>
            </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="计划" name="plan">
            <keep-alive>
                <Plan></Plan>
            </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="日计划" name="daily">
            <keep-alive>
                <Daily></Daily>
            </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="通道" name="channel">
            <keep-alive>
                <Channel></Channel>
            </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="阶段" name="stage">
            <keep-alive>
                <Stage></Stage>
            </keep-alive>
        </el-tab-pane>
    </el-tabs>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { TabsInstance } from 'element-plus'
import Channel from './channel/index.vue'
import Daily from './daily/index.vue'
import Pattern from './pattern/index.vue'
import Phase from './phase/index.vue'
import Plan from './plan/index.vue'
import Stage from './stage/index.vue'

const tabPosition = ref<TabsInstance['tabPosition']>('left')
const activeTab = ref('phase')
</script>

<style>
.demo-tabs>.el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}

.el-tabs--right .el-tabs__content,
.el-tabs--left .el-tabs__content {
    height: 100%;
}
</style>
