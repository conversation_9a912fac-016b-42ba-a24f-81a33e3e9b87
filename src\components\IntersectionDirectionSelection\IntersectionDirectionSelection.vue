<template>
  <div class="intersection-direction-selection" v-if="isSpecialIntersection !== undefined">
    <intersection-base-map
            v-if="!isSpecialIntersection && reset"
            ref="intersectionMap2"
            :crossStatusData="crossStatusData"
            :agentId="agentId"
            isVipRoute
            :clickMode="clickMode"
            :isThirdSignal="isThirdSignal"
            :choosedDirection="choosedDirection"
            :choosedPedDirection="choosedPedDirection"
            :roadDirection="roadDirection"
            @handleClickCrossIcon="handleClickCrossIcon" />
    <direction-list-configuration
      v-if="isSpecialIntersection && clickMode && reset"
      ref="listConfiguration"
      :agentId="agentId"
      :isThirdSignal="isThirdSignal"
      :choosedDirection="choosedDirection"
      :choosedPedDirection="choosedPedDirection"
      :roadDirection="roadDirection"
      @handleClickCrossIcon="handleClickCrossIcon" />
  </div>
</template>
<script>
import { queryDevice } from '../../../api/control.js'
import { getIntersectionInfo } from '../../../api/template.js'
import { getMessageByCode } from '../../../utils/responseMessage.js'
import {
  setToken
} from '../../../utils/auth'
export default {
  name: 'intersection-direction-selection',
  data () {
    return {
      crossStatusData: {},
      isSpecialIntersection: undefined,
      isThirdSignal: false,
      reset: true
    }
  },
  props: {
    agentId: {
      type: String,
      required: true
    },
    roadDirection: {
      type: String,
      default: 'right'
    },
    choosedDirection: {
      type: Array
    },
    choosedPedDirection: {
      type: Array
    },
    thirdSignal: { // 是否是第三方平台（可以直接传，也可以内部接口判断）
      type: Boolean
    },
    clickMode: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    agentId: {
      handler: function (val, oldVal) {
        if (val) {
          this.reset = false
          this.$nextTick(() => {
            this.reset = true
            this.initSelection()
          })
        }
      }
    }
  },
  methods: {
    setPropsToken (token) {
      // 获取组件外传入的token，便于独立组件调用接口
      if (token && token !== '') {
        setToken(token)
      }
    },
    getIntersectionInfo () {
      // 获取路口信息
      const agentid = this.agentId || '0'
      getIntersectionInfo(agentid).then(res => {
        if (!res.data.success) {
          let commomMsg = this.$t('openatccomponents.overview.signalID') + ' : ' + agentid
          let msg = getMessageByCode(res.data.code, this.$i18n.locale)
          if (res.data.data) {
            // 子类型错误
            let childErrorCode = res.data.data.errorCode
            if (childErrorCode) {
              let childerror = getMessageByCode(res.data.data.errorCode, this.$i18n.locale)
              msg = msg + ' - ' + childerror
            }
          }
          msg = msg + ' - ' + commomMsg
          this.$message.error(msg)
          return
        }
        this.tempType = res.data.data.type
        this.mainType = this.tempType.split('-')[0]
        if (this.mainType === '999') {
          this.isSpecialIntersection = true
          if (this.clickMode) {
            this.$nextTick(() => {
              this.$refs.listConfiguration.init(res)
            })
          }
        } else {
          this.isSpecialIntersection = false
        }
      })
    },
    getPlatform () {
      queryDevice(this.agentId).then(res => {
        if (!res.data.success) {
          let commomMsg = this.$t('openatccomponents.overview.signalID') + ': ' + this.AgentId
          this.$message.error(getMessageByCode(res.data.code, this.$i18n.locale) + ' - ' + commomMsg)
          return
        }
        this.platform = res.data.data.platform
        console.log(this.platform)
        if (this.platform !== '' && this.platform !== 'OpenATC') {
          this.isThirdSignal = true
        } else {
          this.isThirdSignal = false
        }
        if (this.thirdSignal) {
          this.isThirdSignal = this.thirdSignal
        }
      })
    },
    handleClickCrossIcon (allChoosedDir, curClickedPhase) {
      this.$emit('handleClickCrossIcon', allChoosedDir, curClickedPhase)
    },
    initSelection () {
      this.getIntersectionInfo()
      this.getPlatform()
    },
    refresh () {
      // 刷新底图
      this.$refs.intersectionMap2.resetCrossDiagram()
    }
  },
  mounted () {
    this.initSelection()
  },
  destroyed () {
  }
}
</script>
<style lang='scss'>
  .intersection-direction-selection {
    width: 100%;
    height: 100%
  }
</style>
