// 行人SVG组件类型定义

/**
 * 行人相位ID类型
 * 支持的行人相位ID范围：1-16
 */
export type PedPhaseId = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16;

/**
 * 行人相位映射接口
 */
export interface PedPhaseMapping {
  id: PedPhaseId;
  name: string;
  description: string;
  component: string;
}

/**
 * 行人相位映射数据
 */
export const PED_PHASE_MAPPINGS: PedPhaseMapping[] = [
  { id: 1, name: '东行人', description: '东向行人通行', component: 'PedEastward' },
  { id: 2, name: '西行人', description: '西向行人通行', component: 'PedWestward' },
  { id: 3, name: '南行人', description: '南向行人通行', component: 'PedSouthward' },
  { id: 4, name: '北行人', description: '北向行人通行', component: 'PedNorthward' },
  { id: 5, name: '东行人-上', description: '东向行人通行（上侧）', component: 'PedEastTop' },
  { id: 6, name: '东行人-下', description: '东向行人通行（下侧）', component: 'PedEastBottom' },
  { id: 7, name: '西行人-上', description: '西向行人通行（上侧）', component: 'PedWestTop' },
  { id: 8, name: '西行人-下', description: '西向行人通行（下侧）', component: 'PedWestBottom' },
  { id: 9, name: '南行人-左', description: '南向行人通行（左侧）', component: 'PedSouthLeft' },
  { id: 10, name: '南行人-右', description: '南向行人通行（右侧）', component: 'PedSouthRight' },
  { id: 11, name: '北行人-左', description: '北向行人通行（左侧）', component: 'PedNorthLeft' },
  { id: 12, name: '北行人-右', description: '北向行人通行（右侧）', component: 'PedNorthRight' },
  { id: 13, name: 'X行人-/', description: '斜向行人通行（/方向）', component: 'PedXR' },
  { id: 14, name: 'X行人-\\', description: '斜向行人通行（\\方向）', component: 'PedXl' },
  { id: 15, name: '东西路段行人', description: '东西向路段行人通行', component: 'PedNS' },
  { id: 16, name: '南北路段行人', description: '南北向路段行人通行', component: 'PedEW' }
];

/**
 * 验证行人相位ID是否有效
 * @param id 行人相位ID
 * @returns 是否有效
 */
export function isValidPedPhaseId(id: number): id is PedPhaseId {
  return id >= 1 && id <= 16 && Number.isInteger(id);
}

/**
 * 根据行人相位ID获取映射信息
 * @param id 行人相位ID
 * @returns 行人相位映射信息或undefined
 */
export function getPedPhaseMappingById(id: PedPhaseId): PedPhaseMapping | undefined {
  return PED_PHASE_MAPPINGS.find(mapping => mapping.id === id);
}

/**
 * 获取所有有效的行人相位ID
 * @returns 行人相位ID数组
 */
export function getAllPedPhaseIds(): PedPhaseId[] {
  return PED_PHASE_MAPPINGS.map(mapping => mapping.id);
}

/**
 * 根据名称搜索行人相位
 * @param name 行人相位名称（支持部分匹配）
 * @returns 匹配的行人相位映射数组
 */
export function searchPedPhasesByName(name: string): PedPhaseMapping[] {
  return PED_PHASE_MAPPINGS.filter(mapping => 
    mapping.name.toLowerCase().includes(name.toLowerCase()) ||
    mapping.description.toLowerCase().includes(name.toLowerCase())
  );
}
