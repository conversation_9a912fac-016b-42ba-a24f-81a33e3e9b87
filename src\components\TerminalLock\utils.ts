import { postDevsMessage } from '@/api/business/signalManagement';
import type { MessageData } from '@/api/business/signalManagement/types';
import zh_CN from '@/lang/zh_CN';

/**
 * 上传单个TSC参数
 * @param messageData 消息数据
 * @returns Promise对象
 */
export const uploadSingleTscParam = (messageData: MessageData): Promise<any> => {
  return postDevsMessage(messageData);
};

export const uploadTscParam = (messageData: MessageData): Promise<any> => {
  return postDevsMessage(messageData);
};

/**
 * 根据错误代码获取消息（仅中文）
 * @param code 错误代码
 * @returns 错误消息
 */
export function getMessageByCode(code: string | number): string {
  let res = zh_CN.platform.message.errorcode + code;
  let message = zh_CN.platform.message
  if (code) {
    if (message[code]) {
      res = message[code]
    }
  }
  return res;
}
