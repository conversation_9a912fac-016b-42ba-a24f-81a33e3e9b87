<!--行人SVG图标组件-->
<template>
  <div class="ped-svg">
    <PedEastward v-if="pedId === 1" />
    <PedWestward v-if="pedId === 2" />
    <PedSouthward v-if="pedId === 3" />
    <PedNorthward v-if="pedId === 4" />
    <PedEastTop v-if="pedId === 5" />
    <PedEastBottom v-if="pedId === 6" />
    <PedWestTop v-if="pedId === 7" />
    <PedWestBottom v-if="pedId === 8" />
    <PedSouthLeft v-if="pedId === 9" />
    <PedSouthRight v-if="pedId === 10" />
    <PedNorthLeft v-if="pedId === 11" />
    <PedNorthRight v-if="pedId === 12" />
    <PedXR v-if="pedId === 13" />
    <PedXl v-if="pedId === 14" />
    <PedNS v-if="pedId === 15" />
    <PedEW v-if="pedId === 16" />
  </div>
</template>

<script lang="ts">
import { defineComponent, type PropType } from 'vue';
import PedEastward from './ped-pedeastward.vue';
import PedWestward from './ped-pedwestward.vue';
import PedNorthward from './ped-pednorthward.vue';
import PedSouthward from './ped-pedsouthward.vue';
import PedEastTop from './ped-east-top.vue';
import PedEastBottom from './ped-east-bottom.vue';
import PedWestTop from './ped-west-top.vue';
import PedWestBottom from './ped-west-bottom.vue';
import PedSouthLeft from './ped-south-left.vue';
import PedSouthRight from './ped-south-right.vue';
import PedNorthLeft from './ped-north-left.vue';
import PedNorthRight from './ped-north-right.vue';
import PedEW from './ped-ewped.vue';
import PedNS from './ped-snped.vue';
import PedXl from './ped-xlped.vue';
import PedXR from './ped-xrped.vue';
import type { PedPhaseId } from './types';

export default defineComponent({
  name: 'PedSvg',
  components: {
    PedEastward,
    PedWestward,
    PedNorthward,
    PedSouthward,
    PedEastTop,
    PedEastBottom,
    PedWestTop,
    PedWestBottom,
    PedSouthLeft,
    PedSouthRight,
    PedNorthLeft,
    PedNorthRight,
    PedEW,
    PedNS,
    PedXl,
    PedXR
  },
  props: {
    /**
     * 行人相位ID
     * 1: 东行人, 2: 西行人, 3: 南行人, 4: 北行人
     * 5: 东行人-上, 6: 东行人-下, 7: 西行人-上, 8: 西行人-下
     * 9: 南行人-左, 10: 南行人-右, 11: 北行人-左, 12: 北行人-右
     * 13: X行人-/, 14: X行人-\, 15: 东西路段行人, 16: 南北路段行人
     */
    pedId: {
      type: Number as PropType<PedPhaseId>,
      required: true,
      validator: (value: number): boolean => {
        return value >= 1 && value <= 16;
      }
    }
  }
});
</script>

<style lang="scss" scoped>
.ped-svg {
  display: inline-block;
  width: 100%;
  height: 100%;

  // 确保SVG图标居中显示
  display: flex;
  align-items: center;
  justify-content: center;

  // SVG图标的基础样式
  :deep(svg) {
    width: 100%;
    height: 100%;
    max-width: 32px;
    max-height: 32px;
  }
}
</style>
