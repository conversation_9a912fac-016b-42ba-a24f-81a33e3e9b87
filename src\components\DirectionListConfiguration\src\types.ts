// 方向列表配置组件类型定义

import type { RoadDirection } from '@/components/IntersectionMap/crossDirection/types';

/**
 * 组件 Props 接口
 */
export interface DirectionListConfigurationProps {
  /** 标签宽度 */
  labelWidth: string;
  /** 代理ID */
  agentId?: string;
  /** 列表数据 */
  list: any[];
  /** 道路行车方向 */
  roadDirection: RoadDirection;
  /** 是否为第三方信号机 */
  isThirdSignal: boolean;
  /** 已选择的方向 */
  choosedDirection?: number[];
  /** 已选择的行人方向 */
  choosedPedDirection?: number[];
}

/**
 * 相位数据项接口
 */
export interface PhaseDataItem {
  key?: string;
  phaseid?: number;
  id: number;
  name: string;
  left?: number;
  top?: number;
  type?: number | string;
  color?: string;
  phaseCountdown?: number;
  flag?: string;
  controltype?: number;
  channelid?: number;
  iconclass?: string;
  disabled?: boolean;
}

/**
 * 行人相位数据项接口
 */
export interface PedPhaseDataItem {
  key?: string;
  phaseid?: number;
  id: number;
  name: string;
  left?: number;
  top?: number;
  pedtype?: number | string;
  color?: string;
  flag?: string;
  channelid?: number;
  disabled?: boolean;
}

/**
 * 通道数据项接口
 */
export interface ChannelDataItem {
  id: number;
  type: number;
  realdir?: number[];
  status?: number;
}

/**
 * 路口信息接口
 */
export interface IntersectionInfo {
  data: {
    success: boolean;
    code: string | number;
    data: {
      type: string;
      param: {
        phaseList: PhaseListItem[];
        overlaplList?: OverlapListItem[];
      };
    };
  };
}

/**
 * 相位列表项接口
 */
export interface PhaseListItem {
  id: number;
  direction: number[];
  peddirection?: number[];
  controltype?: number;
}

/**
 * 跟随相位列表项接口
 */
export interface OverlapListItem {
  id: number;
  direction?: number[];
  peddirection?: number[];
  mphase: number[];
}

/**
 * 图标配置接口
 */
export interface IconConfig {
  id: number;
  class: string;
  leftclass?: string;
}

/**
 * 选择方向事件数据接口
 */
export interface SelectDirectionData {
  direction: number[];
  peddirection: number[];
}

/**
 * 组件数据接口
 */
export interface DirectionListConfigurationData {
  laneList: PhaseDataItem[];
  pedList: PedPhaseDataItem[];
  preselectDirection: number[];
  preselectPedDirection: number[];
  CrossDiagramMgr?: any;
  PhaseDataModel?: any;
  tempType?: string;
  mainType?: string;
  mainDirection?: string;
  crossInfo?: any;
  LanePhaseData: PhaseDataItem[];
  overlapLanePhaseData: PhaseDataItem[];
  sidewalkPhaseData: PedPhaseDataItem[];
  overlapsidewalkPhaseData: PedPhaseDataItem[];
  compLanePhaseData: PhaseDataItem[];
  compSidewalkPhaseData: PedPhaseDataItem[];
  inneChoosedDirection: number[];
  inneChoosedPedDirection: number[];
  channelList: ChannelDataItem[];
  sidewalkDir: number[];
  phaseConflictList: number[];
  pedConflictList: number[];
  choosedLanePhase: PhaseDataItem[];
  choosedPedPhase: PedPhaseDataItem[];
}
