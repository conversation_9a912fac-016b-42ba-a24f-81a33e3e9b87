import { WebrtcPlayer } from '../webrtc/index'
import type { PlayOptions, UsePlayerReturn, IWebrtcPlayer } from '../types/index'

/**
 * Hook for using the player.
 */
export const usePlayer = (): UsePlayerReturn => {
  let play: IWebrtcPlayer | null = null

  /**
   * Replay method to handle playing based on conditions.
   * @param id - The ID of the stream.
   * @param playOpt - The play options.
   */
  const replay = (id: string, playOpt: PlayOptions): void => {
    if (play) {
      play.unPlay()
    }

    if (playOpt.type === 1) {
      play = new WebrtcPlayer()
    }

    if (play) {
      play.play(id, playOpt)
    }
  }

  return {
    replay,
    webrtcPlayer: play
  }
}
