import { mergedirections, postDevsMessage, validdirections } from '@/api/business/signalManagement';
import type { MessageData } from '@/api/business/signalManagement/types';
import zh_CN from '@/lang/zh_CN';
import type PhaseDataModel from '@/components/PhaseDataModel';
import type CrossDiagramMgr from '@/components/EdgeMgr/controller/crossDiagramMgr';

/**
 * 上传单个TSC参数
 * @param messageData 消息数据
 * @returns Promise对象
 */
export const uploadSingleTscParam = (messageData: MessageData): Promise<any> => {
  return postDevsMessage(messageData);
};

export const uploadTscParam = (messageData: MessageData): Promise<any> => {
  return postDevsMessage(messageData);
};

export const getValidDirections = (id: string | number): Promise<any> => {
  return validdirections(id);
};

export const getMergeDirections = (id: string | number): Promise<any> => {
  return mergedirections(id);
};

/**
 * 根据错误代码获取消息（仅中文）
 * @param code 错误代码
 * @returns 错误消息
 */
export function getMessageByCode(code: string | number): string {
  let res = zh_CN.platform.message.errorcode + code;
  let message = zh_CN.platform.message;
  if (code) {
    if (message[code]) {
      res = message[code];
    }
  }
  return res;
}

/**
 * 获取行人方向显示数据
 * @param peddirection 行人方向数组
 * @param phaseDataModel 相位数据模型
 * @returns 格式化的行人方向数据
 */
export function getShowPedDirection(peddirection: number[], phaseDataModel: PhaseDataModel): any[] {
  if (!phaseDataModel || !peddirection) return [];

  const peddirarr = peddirection.map(peddir => ({
    id: peddir,
    name: phaseDataModel.getSidePos(peddir)?.name || '',
    color: 'rgba(255, 255, 255, 0.4)'
  }));
  return peddirarr;
}

/**
 * 获取有效方向位置数据
 * @param effectiveDirection 有效方向数组
 * @param phaseDataModel 相位数据模型
 * @param crossDiagramMgr 路口图管理器
 * @returns 有效方向位置数据数组
 */
export function getEffectDirectionPositions(
  effectiveDirection: number[],
  phaseDataModel: PhaseDataModel,
  crossDiagramMgr: CrossDiagramMgr
): any[] {
  if (!phaseDataModel || !crossDiagramMgr || !effectiveDirection) return [];

  const effectDirData: any[] = [];
  effectiveDirection.forEach((dir) => {
    const pos = phaseDataModel.getEffectPos ? phaseDataModel.getEffectPos(dir) : null;
    if (pos) {
      effectDirData.push({
        key: crossDiagramMgr.getUniqueKey('effectiveDir'),
        id: dir,
        name: pos.name,
        left: pos.x,
        top: pos.y,
        color: '#6e737c'
      });
    }
  });
  return effectDirData;
}

/**
 * 处理有效方向数据获取
 * @param agentId 设备ID
 * @param onSuccess 成功回调
 * @param onError 错误回调
 */
export function handleGetValidDirections(
  agentId: string,
  onSuccess: (validDirections: number[]) => void,
  onError?: (message: string) => void
): void {
  if (!agentId) return;

  getValidDirections(agentId).then(data => {
    if (!data.data.success) {
      const errorMsg = getMessageByCode(data.data.code);
      if (onError) {
        onError(errorMsg);
      } else {
        console.log(errorMsg);
      }
      return;
    }
    const validDirections = data.data.data.validDirections || [];
    onSuccess(validDirections);
  }).catch(error => {
    const errorMsg = '获取有效方向失败';
    if (onError) {
      onError(errorMsg);
    } else {
      console.error(errorMsg, error);
    }
  });
}

/**
 * 处理合并方向数据获取
 * @param agentId 设备ID
 * @param onSuccess 成功回调
 * @param onError 错误回调
 */
export function handleGetMergeDirections(
  agentId: string,
  onSuccess: (mergeDirections: number[]) => void,
  onError?: (message: string) => void
): void {
  if (!agentId) return;

  getMergeDirections(agentId).then(data => {
    if (!data.data.success) {
      const errorMsg = getMessageByCode(data.data.code);
      if (onError) {
        onError(errorMsg);
      } else {
        console.log(errorMsg);
      }
      return;
    }
    const mergeDirections = data.data.data.mergeDirections || [];
    onSuccess(mergeDirections);
  }).catch(error => {
    const errorMsg = '获取合并方向失败';
    if (onError) {
      onError(errorMsg);
    } else {
      console.error(errorMsg, error);
    }
  });
}

/**
 * 数组比较工具函数
 * @param arr1 第一个数组
 * @param arr2 第二个数组
 * @returns 是否相等
 */
export function isArraysEqual(arr1: number[], arr2: number[]): boolean {
  if (arr1.length !== arr2.length) return false;
  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] !== arr2[i]) return false;
  }
  return true;
}

/**
 * 获取路口类型
 * @param mainType 主类型
 * @param mainDirection 主方向
 * @returns 路口类型
 */
export function getCrossType(mainType: string, mainDirection: string): string {
  // 路口类型对应底图决策
  if (mainType === '101') {
    // 十字路口
    return 'Crossroads';
  }
  if (mainType === '100') {
    // T型路口
    switch (mainDirection) {
      case '001': return 'TypeT-east';
      case '002': return 'TypeT-south';
      case '003': return 'TypeT-west';
      case '004': return 'TypeT-north';
      default: return '';
    }
  }
  if (mainType === '103') {
    // 匝道
    switch (mainDirection) {
      case '001': return 'ramp-east';
      case '002': return 'ramp-south';
      case '003': return 'ramp-west';
      case '004': return 'ramp-north';
      default: return '';
    }
  }
  if (mainType === '104') {
    // 路段行人过街
    switch (mainDirection) {
      case '005': return 'ped-section-east-west';
      case '006': return 'ped-section-south-north';
      default: return '';
    }
  }
  if (mainType === '999') {
    // 其他路口
    return 'Customroads';
  }
  return '';
}

/**
 * 创建相位状态映射
 * @param phaseStatusList 相位状态列表
 * @returns 相位状态映射
 */
export function createPhaseStatusMap(phaseStatusList: any[]): Map<number, any> {
  const phaseStatusMap = new Map();
  phaseStatusList.forEach(phase => {
    const phaseId = phase.id;
    const phaseInfo = {
      type: phase.type,
      phaseCountdown: phase.countdown,
      pedtype: phase.pedtype
    };
    phaseStatusMap.set(phaseId, phaseInfo);
  });
  return phaseStatusMap;
}

/**
 * 设置数据颜色为默认白色
 * @param dataList 数据列表
 * @returns 更新后的数据列表
 */
export function setDefaultColor(dataList: any[]): any[] {
  return dataList.map(data => ({
    ...data,
    color: '#fff'
  }));
}

/**
 * 设置数据为黄闪状态
 * @param dataList 数据列表
 * @param typeField 类型字段名
 * @returns 更新后的数据列表
 */
export function setYellowFlashStatus(dataList: any[], typeField: string = 'type'): any[] {
  return dataList.map(data => ({
    ...data,
    [typeField]: '黄闪',
    control: 1
  }));
}

/**
 * 设置数据特殊控制状态颜色
 * @param dataList 数据列表
 * @param controlColorMap 控制颜色映射
 * @param control 控制类型
 * @returns 更新后的数据列表
 */
export function setSpecialControlColor(
  dataList: any[],
  controlColorMap: Map<string, string>,
  control: string
): any[] {
  return dataList.map(data => ({
    ...data,
    color: controlColorMap.get(control)
  }));
}

/**
 * 处理相位状态数据
 * @param phaseData 相位数据列表
 * @param phaseStatusMap 相位状态映射
 * @param colorMap 颜色映射
 * @param flag 标识
 * @returns 处理后的相位数据
 */
export function processPhaseStatus(
  phaseData: any[],
  phaseStatusMap: Map<number, any>,
  colorMap: Map<number, string>,
  flag: string
): any[] {
  const curPhaseData: any[] = [];
  for (let i = 0; i < phaseData.length; i++) {
    const curPhaseStatus = phaseStatusMap.get(phaseData[i].phaseid);
    if (!curPhaseStatus) continue;
    const data = {
      ...phaseData[i],
      type: curPhaseStatus.type,
      color: colorMap.get(curPhaseStatus.type),
      phaseCountdown: curPhaseStatus.phaseCountdown,
      flag: flag
    };
    curPhaseData.push(data);
  }
  return curPhaseData;
}

/**
 * 处理行人相位状态数据
 * @param sidewalkData 行人相位数据列表
 * @param phaseStatusMap 相位状态映射
 * @param colorMap 颜色映射
 * @param flag 标识
 * @returns 处理后的行人相位数据
 */
export function processPedStatus(
  sidewalkData: any[],
  phaseStatusMap: Map<number, any>,
  colorMap: Map<number, string>,
  flag: string
): any[] {
  const curPedStatus: any[] = [];
  for (let i = 0; i < sidewalkData.length; i++) {
    if (sidewalkData[i].phaseid) {
      const curPhaseStatus = phaseStatusMap.get(sidewalkData[i].phaseid);
      if (!curPhaseStatus) continue;
      const data = {
        ...sidewalkData[i],
        pedtype: curPhaseStatus.pedtype,
        color: colorMap.get(curPhaseStatus.pedtype),
        flag: flag
      };
      curPedStatus.push(data);
    } else {
      // 无状态的行人道
      const data = {
        ...sidewalkData[i],
        pedtype: undefined
      };
      curPedStatus.push(data);
    }
  }
  return curPedStatus;
}

/**
 * 去除数组中重复的方向数据
 * @param dataArray 数据数组
 * @returns 去重后的数据数组
 */
export function removeDuplicateDirections(dataArray: any[]): any[] {
  return Array.from(new Set(dataArray.map(item => item.id)))
    .map(id => dataArray.find(item => item.id === id))
    .filter(Boolean);
}

/**
 * 设置方向列表主题
 * @param list 方向列表
 * @returns 设置主题后的列表
 */
export function setDirectionListTheme(list: any[]): any[] {
  const dirArr: any[] = [];
  for (const rec of list) {
    const recd = {
      ...rec,
      color: '#fff'
    };
    dirArr.push(recd);
    for (let i = 0; i < rec.peddirection.length; i++) {
      rec.peddirection[i].color = 'rgba(255, 255, 255, 0.6)';
    }
  }
  return dirArr;
}

/**
 * 处理通道重复方向数据
 * @param channelList 通道列表
 * @returns 处理后的通道列表
 */
export function handleRepeatRealdir(channelList: any[]): any[] {
  // 按realdir去掉重复方向的数据
  const dirChannelList = channelList.filter(ele =>
    ele.realdir !== undefined && (ele.type === 0 || ele.type === 1 || ele.type === 3)
  );
  const pedDirChannelList = channelList.filter(ele =>
    ele.realdir !== undefined && ele.type === 2
  );

  const map = new Map();
  const map2 = new Map();

  dirChannelList.forEach(ele => {
    ele.realdir.forEach((dir: number) => {
      if (map.get(dir) === undefined) {
        map.set(dir, ele);
      }
    });
  });

  pedDirChannelList.forEach(ele => {
    ele.realdir.forEach((dir: number) => {
      if (map2.get(dir) === undefined) {
        map2.set(dir, ele);
      }
    });
  });

  const arr = Array.from(map);
  const pedarr = Array.from(map2);
  const newarr: any[] = [];

  arr.forEach(ele => {
    const obj = {
      ...ele[1],
      realdir: [ele[0]]
    };
    newarr.push(obj);
  });

  pedarr.forEach(ele => {
    const obj = {
      ...ele[1],
      realdir: [ele[0]]
    };
    newarr.push(obj);
  });

  return newarr;
}

/**
 * 创建通道状态映射
 * @param channelStatusList 通道状态列表
 * @returns 通道状态映射
 */
export function createChannelStatusMap(channelStatusList: any[]): Map<number, any> {
  const channelStatusMap = new Map();
  channelStatusList.forEach(channel => {
    const channelId = channel.id;
    const channelInfo = {
      light: channel.light
    };
    channelStatusMap.set(channelId, channelInfo);
  });
  return channelStatusMap;
}

/**
 * 处理通道相位状态
 * @param phaseData 相位数据
 * @param channelStatusMap 通道状态映射
 * @param colorMap 颜色映射
 * @param flag 标识
 * @returns 处理后的相位数据
 */
export function processChannelPhaseStatus(
  phaseData: any[],
  channelStatusMap: Map<number, any>,
  colorMap: Map<number, string>,
  flag: string
): any[] {
  const curPhaseData: any[] = [];
  for (let i = 0; i < phaseData.length; i++) {
    const curPhaseStatus = channelStatusMap.get(phaseData[i].channelid);
    if (!curPhaseStatus) continue;
    const data = {
      ...phaseData[i],
      type: curPhaseStatus.light,
      color: colorMap.get(curPhaseStatus.light),
      flag: flag
    };
    curPhaseData.push(data);
  }
  return curPhaseData;
}

/**
 * 处理通道行人相位状态
 * @param sidewalkData 行人相位数据
 * @param channelStatusMap 通道状态映射
 * @param colorMap 颜色映射
 * @param flag 标识
 * @returns 处理后的行人相位数据
 */
export function processChannelPedStatus(
  sidewalkData: any[],
  channelStatusMap: Map<number, any>,
  colorMap: Map<number, string>,
  flag: string
): any[] {
  const curPedStatus: any[] = [];
  for (let i = 0; i < sidewalkData.length; i++) {
    if (sidewalkData[i].channelid) {
      const curPhaseStatus = channelStatusMap.get(sidewalkData[i].channelid);
      if (!curPhaseStatus) continue;
      const data = {
        ...sidewalkData[i],
        pedtype: curPhaseStatus.light,
        color: colorMap.get(curPhaseStatus.light),
        flag: flag
      };
      curPedStatus.push(data);
    } else {
      // 无状态的行人道
      const data = {
        ...sidewalkData[i],
        pedtype: undefined
      };
      curPedStatus.push(data);
    }
  }
  return curPedStatus;
}
