// Flvjs组件样式

.flvlist {
  position: relative;
  width: 100%;
  height: 100%;

  .noVideo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    background-color: #f5f5f5;
    color: #999;
    font-size: 14px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .flvlist-dropdown {
    .el-dropdown-item {
      &.active {
        background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
      }

      i {
        margin-right: 8px;
      }
    }
  }
}

.flvplayer {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;

  .flv-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    font-size: 14px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;

    .title {
      font-weight: 500;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .list-icon {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        cursor: pointer;
        font-size: 16px;
        padding: 4px;
        border-radius: 2px;
        transition: background-color 0.2s;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        &.icon-shipinshuaxin:hover {
          color: var(--el-color-primary);
        }
      }

      .el-dropdown-link {
        color: #fff;
        cursor: pointer;
        
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }

  .flv-content {
    width: 100%;
    height: 100%;
    position: relative;

    .flv-video,
    .webrtc-video {
      width: 100%;
      height: 100%;
      object-fit: contain;
      background-color: #000;
    }

    .widget-player-vide {
      display: block;
    }
  }
}

// 深色模式适配
html.dark {
  .flvlist {
    .noVideo {
      background-color: var(--el-bg-color-page);
      color: var(--el-text-color-regular);
      border-color: var(--el-border-color);
    }
  }

  .flvplayer {
    .flv-header {
      background-color: rgba(0, 0, 0, 0.8);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .flvplayer {
    .flv-header {
      padding: 6px 8px;
      font-size: 12px;

      .list-icon {
        gap: 4px;

        i {
          font-size: 14px;
          padding: 2px;
        }
      }
    }
  }
}
