<template>
  <div class="pattern-container" ref="containerRef">
    <div class="header-controls">
      <el-button type="primary" @click="handleAdd">
        新增
      </el-button>
      
      <el-radio-group v-model="isStageMode" class="mode-selector">
        <el-radio :label="false">环视图</el-radio>
        <el-radio :label="true">阶段视图</el-radio>
      </el-radio-group>
      
      <span class="tips">方案图支持拖拽和编辑,点击数字修改绿信比,相序只支持同一个Barrier中拖拽修改</span>
      
      <div class="right-controls">
        <el-button type="text" @click="toggleCycleChange">
          周期等比例切换
        </el-button>
        <el-button type="text" @click="handleModeChange">
          环转阶段
        </el-button>
      </div>
    </div>

    <el-table
      :data="patternList"
      :max-height="tableHeight"
      :row-key="getRowKey"
      :expand-row-keys="expandedRows"
      @expand-change="handleExpandChange"
    >
      <el-table-column type="expand">
        <template #default="{ row, $index }">
          <PatternDetail
            :pattern="row"
            :index="$index"
            :is-stage-mode="isStageMode"
            :ring-count="ringCount"
            :phase-list="phaseList"
            @add-stage="handleAddStage"
            @stage-change="handleStageChange"
          />
        </template>
      </el-table-column>
      
      <el-table-column prop="id" label="ID" width="80" align="center" />
      <el-table-column prop="desc" label="描述" />
      <el-table-column prop="cycle" label="周期" width="100" />
      
      <el-table-column label="操作" width="200">
        <template #default="{ row, $index }">
          <el-button type="text" @click="handleOptimize($index, row)">
            优化
          </el-button>
          <el-button type="text" @click="handleDelete($index, row)">
            删除
          </el-button>
          <el-button v-if="isStageMode" type="text" @click="handleClone($index, row)">
            克隆
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 优化对话框 -->
    <PatternOptimizeDialog
      v-model="optimizeDialogVisible"
      :pattern="selectedPattern"
      @optimize="handleOptimizeConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PatternDetail from './components/PatternDetail.vue'
import PatternOptimizeDialog from './components/PatternOptimizeDialog.vue'
import { usePatternStore } from './stores/patternStore'
import type { Pattern, Stage } from './types'

const patternStore = usePatternStore()

// 响应式数据
const containerRef = ref<HTMLElement>()
const tableHeight = ref(760)
const isStageMode = ref(false)
const cycleChangeEnabled = ref(true)
const expandedRows = ref<number[]>([])
const optimizeDialogVisible = ref(false)
const selectedPattern = ref<Pattern | null>(null)

// 计算属性
const patternList = computed(() => patternStore.patternList)
const phaseList = computed(() => patternStore.phaseList)
const ringCount = computed(() => patternStore.ringCount)

// 方法
const getRowKey = (row: Pattern) => row.id

const handleExpandChange = (row: Pattern, expandedRows: Pattern[]) => {
  expandedRows.value = expandedRows.map(r => r.id)
}

const handleAdd = () => {
  if (phaseList.value.length === 0) {
    ElMessage.error('请先配置相位')
    return
  }
  if (patternList.value.length >= 32) {
    ElMessage.error('最多只能添加32个方案')
    return
  }
  patternStore.addPattern()
}

const handleDelete = async (index: number, row: Pattern) => {
  try {
    await ElMessageBox.confirm(
      '确认删除此方案？',
      '警告',
      { type: 'warning' }
    )
    patternStore.deletePattern(row.id)
  } catch {
    // 用户取消删除
  }
}

const handleClone = (index: number, row: Pattern) => {
  if (patternList.value.length >= 32) {
    ElMessage.error('最多只能添加32个方案')
    return
  }
  patternStore.clonePattern(row.id)
}

const handleOptimize = (index: number, row: Pattern) => {
  selectedPattern.value = row
  optimizeDialogVisible.value = true
}

const handleOptimizeConfirm = () => {
  // 优化逻辑
  optimizeDialogVisible.value = false
}

const handleAddStage = (patternId: number) => {
  patternStore.addStage(patternId)
}

const handleStageChange = (patternId: number, stageIndex: number, stage: Stage) => {
  patternStore.updateStage(patternId, stageIndex, stage)
}

const toggleCycleChange = () => {
  cycleChangeEnabled.value = !cycleChangeEnabled.value
}

const handleModeChange = () => {
  // 模式切换逻辑
}

// 监听器
watch(isStageMode, (newValue) => {
  patternStore.setStageMode(newValue)
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    if (containerRef.value) {
      tableHeight.value = containerRef.value.offsetHeight - 90
      window.addEventListener('resize', () => {
        if (containerRef.value) {
          tableHeight.value = containerRef.value.offsetHeight - 90
        }
      })
    }
  })
  patternStore.initializeData()
})
</script>

<style scoped>
.pattern-container {
  height: 100%;
  padding: 20px;
}

.header-controls {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;
}

.mode-selector {
  margin-left: 20px;
}

.tips {
  color: #606266;
  font-size: 14px;
}

.right-controls {
  margin-left: auto;
  display: flex;
  gap: 10px;
}
</style>