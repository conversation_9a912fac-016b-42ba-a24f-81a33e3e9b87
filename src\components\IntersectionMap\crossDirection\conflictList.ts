import { computedRelation } from './conflict';

// 类型定义
interface ResultItem {
  direction: number;
  type: 'phase' | 'pedphase';
  laneConflictList: number[];
  pedConflictList: number[];
}

interface ConflictResult {
  conflictDir: number[];
  conflictPedDir: number[];
}

interface ListConflictResult {
  allConflictDir: number[];
  allPedConflictDir: number[];
}

/**
 * 交叉路口方向冲突列表管理类
 */
export default class CrossDirectionConflictList {
  private agentid: string | number;
  private conflictList: ResultItem[];
  private phaseConflictMap: Map<number, ResultItem>;
  private pedPhaseConflictMap: Map<number, ResultItem>;
  private allConflictDir: number[];
  private allPedConflictDir: number[];
  private choosedDirection: number[];
  private choosedPedDirection: number[];

  /**
   * 构造函数
   * @param agentid 代理ID
   * @param choosedDirection 选择的方向列表
   * @param choosedPedDirection 选择的行人方向列表
   */
  constructor(
    agentid: string | number,
    choosedDirection: number[] = [],
    choosedPedDirection: number[] = []
  ) {
    this.agentid = agentid;
    this.conflictList = [];
    this.phaseConflictMap = new Map<number, ResultItem>();
    this.pedPhaseConflictMap = new Map<number, ResultItem>();
    this.allConflictDir = [];
    this.allPedConflictDir = [];
    this.choosedDirection = choosedDirection;
    this.choosedPedDirection = choosedPedDirection;
  }

  /**
   * 根据代理ID获取冲突列表
   * @returns Promise<ResultItem[] | undefined>
   */
  getConflictListByAgentid(): Promise<ResultItem[] | undefined> {
    return new Promise((resolve, reject) => {
      computedRelation(this.agentid)
        .then(res => {
          if (!res) {
            resolve(undefined);
            return;
          }
          this.conflictList = res;
          this.setAllConflictMap();
          resolve(res);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  /**
   * 设置所有冲突映射
   */
  private setAllConflictMap(): void {
    for (let i = 0; i < this.conflictList.length; i++) {
      const item = this.conflictList[i];
      if (item.type === 'phase') {
        this.phaseConflictMap.set(item.direction, item);
      }
      if (item.type === 'pedphase') {
        this.pedPhaseConflictMap.set(item.direction, item);
      }
    }
  }

  /**
   * 获取单个方向的冲突信息
   * @param dir 方向
   * @param type 类型：'phase' 或 'pedphase'
   * @returns 冲突结果或undefined
   */
  getOneDirConflict(dir: number, type: 'phase' | 'pedphase'): ConflictResult | undefined {
    let conflictDir: number[] = [];
    let conflictPedDir: number[] = [];

    if (type === 'phase') {
      const phaseInfo = this.phaseConflictMap.get(dir);
      if (!phaseInfo) return undefined;
      conflictDir = phaseInfo.laneConflictList;
      conflictPedDir = phaseInfo.pedConflictList;
    }

    if (type === 'pedphase') {
      const pedPhaseInfo = this.pedPhaseConflictMap.get(dir);
      if (!pedPhaseInfo) return undefined;
      conflictDir = pedPhaseInfo.laneConflictList;
      conflictPedDir = pedPhaseInfo.pedConflictList;
    }

    return {
      conflictDir,
      conflictPedDir
    };
  }

  /**
   * 获取列表方向冲突信息
   * @param choosedDirection 选择的方向列表
   * @param choosedPedDirection 选择的行人方向列表
   * @returns 所有冲突方向结果
   */
  getListDirConflict(
    choosedDirection: number[],
    choosedPedDirection: number[]
  ): ListConflictResult {
    // 重置冲突方向数组
    this.allConflictDir = [];
    this.allPedConflictDir = [];

    // 处理车辆方向冲突
    for (let i = 0; i < choosedDirection.length; i++) {
      const phaseConflict = this.getOneDirConflict(choosedDirection[i], 'phase');
      if (phaseConflict) {
        const conflictDirArr = phaseConflict.conflictDir;
        const conflictPedDirArr = phaseConflict.conflictPedDir;
        this.allConflictDir = this.allConflictDir.concat(conflictDirArr);
        this.allPedConflictDir = this.allPedConflictDir.concat(conflictPedDirArr);
      }
    }

    // 处理行人方向冲突
    for (let i = 0; i < choosedPedDirection.length; i++) {
      const pedPhaseConflict = this.getOneDirConflict(choosedPedDirection[i], 'pedphase');
      if (pedPhaseConflict) {
        const conflictDirArr = pedPhaseConflict.conflictDir;
        const conflictPedDirArr = pedPhaseConflict.conflictPedDir;
        this.allConflictDir = this.allConflictDir.concat(conflictDirArr);
        this.allPedConflictDir = this.allPedConflictDir.concat(conflictPedDirArr);
      }
    }

    // 去重
    this.allConflictDir = Array.from(new Set(this.allConflictDir));
    this.allPedConflictDir = Array.from(new Set(this.allPedConflictDir));

    return {
      allConflictDir: this.allConflictDir,
      allPedConflictDir: this.allPedConflictDir
    };
  }

  /**
   * 获取所有冲突方向
   * @returns 所有冲突方向数组
   */
  getAllConflictDir(): number[] {
    return this.allConflictDir;
  }

  /**
   * 获取所有行人冲突方向
   * @returns 所有行人冲突方向数组
   */
  getAllPedConflictDir(): number[] {
    return this.allPedConflictDir;
  }

  /**
   * 获取冲突列表
   * @returns 冲突列表
   */
  getConflictList(): ResultItem[] {
    return this.conflictList;
  }

  /**
   * 获取相位冲突映射
   * @returns 相位冲突映射
   */
  getPhaseConflictMap(): Map<number, ResultItem> {
    return this.phaseConflictMap;
  }

  /**
   * 获取行人相位冲突映射
   * @returns 行人相位冲突映射
   */
  getPedPhaseConflictMap(): Map<number, ResultItem> {
    return this.pedPhaseConflictMap;
  }
}
